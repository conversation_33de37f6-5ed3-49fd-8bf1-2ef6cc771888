/*
此文件为开发者工具生成，生成时间: 2025/8/2上午11:56:59

在 C:\Users\<USER>\WeChatProjects\miniprogram-6\miniprogram\pages\album-management\album-management.wxss 中引入样式
```
@import "./album-management.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
*/
.sk-transparent {
    color: transparent !important;
  }
.sk-text-18-7500-573 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 49.2754rpx;
    position: relative !important;
  }
.sk-text {
    background-origin: content-box !important;
    background-clip: content-box !important;
    background-color: transparent !important;
    color: transparent !important;
    background-repeat: repeat-y !important;
  }
.sk-text-18-7500-353 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 37.6812rpx;
    position: relative !important;
  }
.sk-text-18-7500-939 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 49.2754rpx;
    position: relative !important;
  }
.sk-text-18-7500-339 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 37.6812rpx;
    position: relative !important;
  }
.sk-text-18-7500-602 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 49.2754rpx;
    position: relative !important;
  }
.sk-text-18-7500-729 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 37.6812rpx;
    position: relative !important;
  }
.sk-text-18-7500-873 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 34.7826rpx;
    position: relative !important;
  }
.sk-text-18-7500-31 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 34.7826rpx;
    position: relative !important;
  }
.sk-text-18-7500-9 {
    background-image: linear-gradient(transparent 18.7500%, #EEEEEE 0%, #EEEEEE 81.2500%, transparent 0%) !important;
    background-size: 100% 34.7826rpx;
    position: relative !important;
  }
.sk-image {
    background: #EFEFEF !important;
  }
.sk-pseudo::before, .sk-pseudo::after {
      background: #EFEFEF !important;
      background-image: none !important;
      color: transparent !important;
      border-color: transparent !important;
    }
.sk-pseudo-rect::before, .sk-pseudo-rect::after {
      border-radius: 0 !important;
    }
.sk-pseudo-circle::before, .sk-pseudo-circle::after {
      border-radius: 50% !important;
    }
.sk-container {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: transparent;
  }
