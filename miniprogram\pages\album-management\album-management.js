/**
 * 相册管理页面
 *
 * 功能概述：
 * - 图片展示：支持瀑布流和时间轴两种显示模式
 * - 文件夹管理：创建、重命名、删除自定义文件夹，图片归类
 * - 批量操作：多选图片进行归类、删除、移除等操作
 * - 回收站：软删除机制，支持恢复和永久删除
 * - 性能优化：懒加载、缓存、智能预选等优化策略
 *
 * 作者：建筑师转码农
 * 最后更新：2025-08-02
 */

// ================================
// 依赖引入
// ================================

const db = wx.cloud.database();

// UI工具函数
import {
  showToast, showLoading, hideToast, showError,
  showSuccess, showWarning
} from '../../utils/toast.js';





// 文件夹管理工具
import {
  getAllFolders,
  createCustomFolder,
  removeImageFromFolder,
  toggleImageFavorite
} from '../../utils/folder-manager.js';

// 回收站管理工具
import {
  moveToTrash,
  restoreFromTrash,
  permanentDelete,
  emptyTrash,
  autoCleanTrash
} from '../../utils/trash-manager.js';

// ================================
// 页面主体
// ================================

Page({
  // ================================
  // 数据定义
  // ================================
  data: {
    // -------------------- 图片数据 --------------------
    albumImages: [],              // 相册图片列表
    groupedImages: [],            // 按日期分组的图片数据（时间轴模式使用）
    selectedImages: [],           // 批量选择的图片ID列表

    // -------------------- 分页和加载 --------------------
    page: 0,                     // 当前页码，从0开始
    pageSize: 20,                // 每页加载图片数量
    hasMore: true,               // 是否还有更多图片
    loading: false,              // 是否正在加载
    isUploading: false,          // 是否正在上传
    isLoadingFolders: false,     // 文件夹加载状态
    folderLoadingVersion: 0,     // 文件夹加载版本号，用于避免竞争条件

    // -------------------- 界面状态 --------------------
    currentTab: 'all',           // 当前标签页：'all' | 'folders'
    displayMode: 'waterfall',    // 显示模式：'waterfall' | 'timeline'
    isSelectionMode: false,      // 是否处于批量选择模式
    showTimeAxis: true,          // 是否显示时间轴
    isModeSwitching: false,      // 是否正在切换显示模式

    // -------------------- 文件夹相关 --------------------
    folders: [],                 // 文件夹列表
    currentFolder: null,         // 当前选中的文件夹
    hasCustomFolders: false,     // 是否有自定义文件夹
    classifyFolders: [],         // 归类对话框专用的文件夹数据

    // -------------------- 对话框和菜单 --------------------
    showFolderDialog: false,     // 文件夹选择对话框
    showMoreMenu: false,         // 右上角更多菜单
    showModeMenu: false,         // 显示模式切换菜单
    showImageInfo: false,        // 图片详情对话框
    imageInfo: null,             // 当前查看的图片详情

    // -------------------- 文件夹对话框数据 --------------------
    selectedImageId: null,       // 当前操作的图片ID（单张操作时使用）
    selectedFolderIds: [],       // 选中的文件夹ID数组（支持多选）
    currentImageFolders: [],     // 当前图片所在的文件夹ID数组

    // -------------------- 首页图片管理（兼容旧版本） --------------------
    maxBannerCount: 5,           // 首页最多展示图片数量
    chooseBannerMode: false,     // 首页图片选择模式（已废弃，保持兼容性）
    deleteMode: false,           // 批量删除模式（已废弃，保持兼容性）

    // -------------------- 拖拽排序（预留功能） --------------------
    dragStartIndex: -1,          // 拖拽开始索引
    dragOverIndex: -1,           // 拖拽悬停索引
    isDragging: false,           // 是否正在拖拽
    isSortMode: false,           // 是否处于排序模式
    originalOrder: [],           // 原始排序
    showSortButtons: false,      // 是否显示排序按钮

    // -------------------- 触摸事件（预留功能） --------------------
    touchStartTime: 0,           // 触摸开始时间
    touchStartX: 0,              // 触摸开始X坐标
    touchStartY: 0,              // 触摸开始Y坐标
    currentTouchX: 0,            // 当前触摸X坐标
    currentTouchY: 0,            // 当前触摸Y坐标
    longPressTimer: null,        // 长按定时器
    dragOffsetX: 0,              // 拖拽偏移X
    dragOffsetY: 0,              // 拖拽偏移Y
    lastMoveTime: 0              // 上次移动时间
  },

  // ================================
  // 工具函数
  // ================================

  /**
   * 获取文件夹选中状态
   * @param {string} folderId - 文件夹ID
   * @returns {boolean} 是否选中
   */
  getFolderSelectedState: function(folderId) {
    return this.data.selectedFolderIds.includes(folderId);
  },

  /**
   * 获取图片所在的文件夹ID数组
   * @param {Object} image - 图片对象
   * @returns {Array} 文件夹ID数组
   */
  getCurrentImageFolders: function(image) {
    const folders = [];

    // 如果图片在回收站，只返回回收站
    if (image.isDeleted) {
      return ['folder_trash'];
    }

    // 检查收藏夹
    if (image.isFavorite) {
      folders.push('folder_favorite');
    }

    // 检查首页展示
    if (image.bannerOrder) {
      folders.push('folder_banner');
    }

    // 检查自定义文件夹
    if (image.folderIds && Array.isArray(image.folderIds)) {
      folders.push(...image.folderIds);
    }

    return folders;
  },

  // ================================
  // 生命周期函数
  // ================================

  /**
   * 页面加载
   * 初始化页面状态并加载图片数据
   */
  onLoad: function(options) {
    console.log('🚀 相册管理页面开始加载...');

    // 初始化页面状态
    this.setData({
      loading: true,
      albumImages: [],
      groupedImages: [],
      folders: []
    });

    // 加载图片数据
    this.loadAllImages(true);
  },

  // ================================
  // 后台任务和安全检查
  // ================================

  /**
   * 后台安全检查
   * 轻量级检查，确保系统文件夹存在
   */
  backgroundSafetyCheck: async function() {
    try {
      // 检查关键系统文件夹是否存在
      const favoriteExists = await this.quickCheckSystemFolder('folder_favorite');

      if (!favoriteExists) {
        console.log('🔧 检测到系统文件夹缺失，开始修复...');
        await this.createMissingSystemFolders();
      }

      // 启动自动清理任务（独立执行，不影响主流程）
      this.startAutoCleanup().catch(err => {
        console.warn('后台清理任务失败:', err);
      });

    } catch (error) {
      console.warn('后台安全检查失败，不影响主要功能:', error);
    }
  },

  /**
   * 快速检查系统文件夹是否存在
   * @param {string} folderId - 文件夹ID
   * @returns {boolean} 是否存在
   */
  quickCheckSystemFolder: async function(folderId) {
    try {
      const result = await db.collection('album_folders').doc(folderId).get();
      return !!result.data;
    } catch (error) {
      return false; // 查询失败视为不存在
    }
  },

  /**
   * 创建缺失的系统文件夹
   * 确保收藏夹、首页展示、回收站等系统文件夹存在
   */
  createMissingSystemFolders: async function() {
    const systemFolders = [
      { id: 'folder_favorite', name: '收藏夹', systemType: 'favorite' },
      { id: 'folder_banner', name: '首页展示', systemType: 'banner' },
      { id: 'folder_trash', name: '回收站', systemType: 'trash' }
    ];

    for (const folder of systemFolders) {
      try {
        await db.collection('album_folders').doc(folder.id).set({
          data: {
            name: folder.name,
            type: 'system',
            systemType: folder.systemType,
            createTime: new Date(),
            updateTime: new Date(),
            imageCount: 0
          }
        });
        console.log(`✅ 系统文件夹已修复: ${folder.name}`);
      } catch (error) {
        if (error.errCode !== -502002) { // 忽略"文档已存在"错误
          console.warn(`⚠️ 修复系统文件夹失败: ${folder.name}`, error);
        }
      }
    }
  },

  /**
   * 启动自动清理任务
   * 清理回收站中的过期图片
   */
  startAutoCleanup: async function() {
    try {
      // 执行自动清理（静默执行，不显示提示）
      await autoCleanTrash();
    } catch (error) {
      console.error('自动清理失败:', error);
    }
  },

  // ================================
  // 网络请求重试工具
  // ================================

  /**
   * 网络请求重试机制
   * @param {Function} requestFn - 请求函数
   * @param {number} maxRetries - 最大重试次数
   * @param {string} operation - 操作名称，用于日志
   */
  retryRequest: async function(requestFn, maxRetries = 3, operation = '操作') {
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await requestFn();
      } catch (error) {
        console.warn(`${operation}第${i + 1}次尝试失败:`, error);

        if (i === maxRetries - 1) {
          console.error(`${operation}最终失败，已重试${maxRetries}次`);
          throw error;
        }

        // 指数退避：1秒、2秒、4秒
        const delay = Math.pow(2, i) * 1000;
        console.log(`${operation}将在${delay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  },

  // ================================
  // 智能更新工具方法
  // ================================

  /**
   * 智能局部更新单张图片数据
   * 避免重新加载所有图片，提升性能
   * @param {string} imageId - 图片ID
   * @param {object} updates - 要更新的字段
   */
  updateImageInList: function(imageId, updates) {
    const albumImages = this.data.albumImages.map(img =>
      img._id === imageId ? { ...img, ...updates } : img
    );
    this.setData({ albumImages });

    // 如果是时间轴模式，也更新分组数据
    if (this.data.currentTab === 'all') {
      this.groupImagesByDate();
    }
  },

  /**
   * 批量更新多张图片数据
   * @param {Array} imageIds - 图片ID数组
   * @param {object} updates - 要更新的字段
   */
  batchUpdateImagesInList: function(imageIds, updates) {
    const albumImages = this.data.albumImages.map(img =>
      imageIds.includes(img._id) ? { ...img, ...updates } : img
    );
    this.setData({ albumImages });

    // 如果是时间轴模式，也更新分组数据
    if (this.data.currentTab === 'all') {
      this.groupImagesByDate();
    }
  },

  /**
   * 从列表中移除图片
   * @param {Array} imageIds - 要移除的图片ID数组
   */
  removeImagesFromList: function(imageIds) {
    const albumImages = this.data.albumImages.filter(img =>
      !imageIds.includes(img._id)
    );
    this.setData({ albumImages });

    // 如果是时间轴模式，也更新分组数据
    if (this.data.currentTab === 'all') {
      this.groupImagesByDate();
    }
  },

  /**
   * 更新相关文件夹数量
   * 只更新必要的文件夹，避免全量重新加载
   */
  updateRelevantFolderCounts: async function() {
    try {
      // 只更新当前可能受影响的文件夹
      const foldersToUpdate = ['folder_favorite', 'folder_banner', 'folder_trash'];

      if (this.data.currentFolder && this.data.currentFolder.type === 'custom') {
        foldersToUpdate.push(this.data.currentFolder._id);
      }

      // 并行更新文件夹数量
      const updatePromises = foldersToUpdate.map(async (folderId) => {
        const folder = this.data.folders.find(f => f._id === folderId);
        if (folder) {
          const count = await this.updateSingleFolderCount(folderId, folder.type, folder.systemType);
          folder.imageCount = count;
        }
      });

      await Promise.allSettled(updatePromises);

      // 更新文件夹显示
      if (this.data.currentTab === 'folders') {
        // 🔧 修复：确保hasCustomFolders状态正确
        const hasCustomFolders = this.data.folders.some(folder => folder.type === 'custom');
        this.setData({
          folders: this.data.folders,
          hasCustomFolders: hasCustomFolders // 🔧 修复：确保自定义文件夹显示状态正确
        });
      }
    } catch (error) {
      console.warn('更新文件夹数量失败:', error);
    }
  },

  // ================================
  // 数据加载函数
  // ================================

  /**
   * 统一的图片加载入口
   * 根据当前标签页和文件夹状态调用相应的加载方法
   * @param {boolean} reset - 是否重置分页
   */
  loadAlbumImages: function(reset = false) {
    // 检查是否还有更多数据需要加载
    if (!reset && !this.data.hasMore) return;

    const { currentTab, currentFolder } = this.data;

    // 根据不同标签页调用不同的加载方法
    if (currentTab === 'folders' && currentFolder) {
      this.loadFolderImages(reset);
    } else {
      this.loadAllImages(reset);
    }
  },

  /**
   * 加载全部图片
   * @param {boolean} reset - 是否重置分页
   */
  loadAllImages: function(reset = false) {
    console.log('🚀 开始加载图片，reset:', reset);
    this.setData({ loading: true });

    // 处理分页逻辑
    let page = reset ? 0 : this.data.page;
    if (reset) {
      this.setData({ albumImages: [], hasMore: true, page: 0 });
    }

    console.log('🔍 开始查询数据库...');
    const startTime = Date.now();

    // 查询未删除的图片
    db.collection('album_images')
      .where({
        isDeleted: false  // 只查询未删除的图片
      })
      .orderBy('createTime', 'desc')
      .skip(page * this.data.pageSize)
      .limit(this.data.pageSize)
      .get()
      .then(res => {
        const queryTime = Date.now() - startTime;
        console.log(`✅ 数据库查询完成，耗时: ${queryTime}ms，获取到 ${res.data.length} 张图片`);
        this.processImageResults(res.data, reset, page);
      })
      .catch(error => {
        const queryTime = Date.now() - startTime;
        console.error(`❌ 数据库查询失败，耗时: ${queryTime}ms`, error);
        this.setData({ loading: false });
        showError(this, '加载图片失败');
      });
  },





  /**
   * 加载文件夹中的图片
   * @param {boolean} reset - 是否重置分页
   */
  loadFolderImages: function(reset = false) {


    if (this.data.loading || (!this.data.hasMore && !reset)) {
      console.log('⚠️ loadFolderImages 跳过执行:', {
        loading: this.data.loading,
        hasMore: this.data.hasMore,
        reset
      });
      return;
    }

    this.setData({ loading: true });

    // 处理分页逻辑
    let page = reset ? 0 : this.data.page;
    if (reset) {
      this.setData({ albumImages: [], hasMore: true, page: 0 });
    }

    const { currentFolder } = this.data;

    if (!currentFolder) {
      console.error('🚨 currentFolder 为空！');
      this.setData({ loading: false });
      return;
    }

    console.log('📁 加载文件夹图片:', {
      folderName: currentFolder.name,
      folderType: currentFolder.type,
      systemType: currentFolder.systemType,
      folderId: currentFolder._id,
      page,
      pageSize: this.data.pageSize
    });

    let query;

    // 根据文件夹类型构建查询条件
    if (currentFolder.systemType === 'favorite') {
      // 收藏夹：查询收藏的图片
      query = db.collection('album_images').where({
        isFavorite: true,
        isDeleted: db.command.neq(true)
      });
    } else if (currentFolder.systemType === 'banner') {
      // 首页展示：查询有bannerOrder的图片
      query = db.collection('album_images').where({
        bannerOrder: db.command.neq(null),
        isDeleted: db.command.neq(true)
      });
    } else if (currentFolder.systemType === 'trash') {
      // 回收站：查询已删除的图片
      query = db.collection('album_images').where({
        isDeleted: true
      });
    } else {
      // 自定义文件夹：查询包含该文件夹ID的图片
      query = db.collection('album_images').where({
        folderIds: db.command.in([currentFolder._id]),
        isDeleted: db.command.neq(true)
      });
    }

    // 根据文件夹类型选择排序方式
    if (currentFolder.systemType === 'banner') {
      query = query.orderBy('bannerOrder', 'asc');  // 首页展示按顺序排列
    } else {
      query = query.orderBy('createTime', 'desc');  // 其他按时间倒序
    }

    // 执行查询
    console.log('🔍 执行数据库查询:', {
      skip: page * this.data.pageSize,
      limit: this.data.pageSize
    });

    query.skip(page * this.data.pageSize)
      .limit(this.data.pageSize)
      .get()
      .then(res => {
        console.log('📊 查询结果:', {
          数据条数: res.data.length,
          数据: res.data.map(img => ({ id: img._id, fileID: img.fileID }))
        });
        this.processImageResults(res.data, reset, page);
      })
      .catch(error => {
        console.error('🚨 加载文件夹图片失败:', error);
        this.setData({ loading: false });
        showError(this, '加载图片失败');
      });
  },



  /**
   * 处理图片查询结果
   * 获取临时URL并更新页面数据
   * @param {Array} images - 图片数据数组
   * @param {boolean} reset - 是否重置数据
   * @param {number} page - 当前页码
   */
  processImageResults: function(images, reset, page) {
    if (images.length === 0) {
      this.setData({
        hasMore: false,
        loading: false,
        albumImages: reset ? [] : this.data.albumImages,
        groupedImages: reset ? [] : this.data.groupedImages
      });
      return;
    }

    // 过滤有效的fileID
    const validImages = images.filter(item => item.fileID && item.fileID.trim());
    const fileList = validImages.map(item => item.fileID);

    if (fileList.length === 0) {
      console.warn('没有有效的fileID');
      this.setData({ loading: false });
      return;
    }

    // 获取临时URL
    wx.cloud.getTempFileURL({
      fileList: fileList,
      success: (urlRes) => {
        console.log(`✅ 临时链接获取完成`);

        // 创建URL映射
        const urlMap = {};
        urlRes.fileList.forEach(file => {
          urlMap[file.fileID] = file.tempFileURL;
        });

        // 处理图片数据
        const newImages = validImages.map(item => ({
          ...item,
          tempFileURL: urlMap[item.fileID] || '',
          bannerOrder: item.bannerOrder || null,
          isFavorite: item.isFavorite || false,
          folderIds: item.folderIds || [],
          selected: false // 用于批量选择
        }));

        // 合并数据
        const albumImages = reset ? newImages : this.data.albumImages.concat(newImages);

        // 更新页面数据

        this.setData({
          albumImages,
          hasMore: newImages.length === this.data.pageSize,
          page: page + 1,
          loading: false
        });



        // 🎬 添加图片加载动画
        this.triggerImageLoadAnimation(newImages);

        // 🔧 修复：文件夹内部也需要支持时间轴分组
        if (this.data.displayMode === 'timeline') {
          this.groupImagesByDate();
        }
      },
      fail: (error) => {
        console.error('获取临时链接失败:', error);
        this.setData({ loading: false });
        showError(this, '获取图片链接失败');
      }
    });
  },

  /**
   * 🎬 触发图片加载动画
   * @param {Array} newImages - 新加载的图片数组
   */
  triggerImageLoadAnimation: function(newImages) {
    // 🔧 修复：瀑布流模式也需要设置loaded状态，否则图片不显示
    if (this.data.displayMode === 'waterfall') {
      console.log('🌊 瀑布流模式：直接设置loaded状态，跳过动画');
      // 瀑布流模式：立即设置loaded状态，不需要动画延迟
      const albumImages = this.data.albumImages.map(img => {
        if (newImages.some(newImg => newImg._id === img._id)) {
          return { ...img, loaded: true };
        }
        return img;
      });
      this.setData({ albumImages });
      return;
    }

    // 延迟添加loaded类，创建丝滑的加载动画（仅时间轴模式）
    setTimeout(() => {
      const albumImages = this.data.albumImages.map(img => {
        if (newImages.some(newImg => newImg._id === img._id)) {
          return { ...img, loaded: true };
        }
        return img;
      });

      this.setData({ albumImages });
    }, 100); // 100ms延迟，让DOM先渲染
  },

  /**
   * 🎬 显示模式切换动画
   * @param {string} fromMode - 当前模式
   * @param {string} toMode - 目标模式
   */
  animateDisplayModeSwitch: function(fromMode, toMode) {
    console.log(`🎬 开始显示模式切换动画: ${fromMode} → ${toMode}`);

    // 添加切换动画状态
    this.setData({
      isModeSwitching: true
    });

    // 动画完成后清除状态
    setTimeout(() => {
      this.setData({
        isModeSwitching: false
      });
    }, 400); // 与CSS动画时间一致
  },

  // ================================
  // 页面事件处理
  // ================================

  /**
   * 页面滚动到底部时自动加载下一页
   */
  onReachBottom: function() {
    this.loadAlbumImages();
  },

  /**
   * 瀑布流组件滚动到底部时自动加载下一页
   * 修复：瀑布流无法滑动到底的问题
   */
  onWaterfallScrollToLower: function() {
    console.log('🌊 瀑布流滚动到底部，开始加载更多图片');
    this.loadAlbumImages();
  },

  // ================================
  // 显示模式切换
  // ================================

  /**
   * 切换显示模式（瀑布流/时间轴）
   * @param {Object} e - 事件对象（可选，从菜单调用时为空）
   */
  switchDisplayMode: function(e) {
    let newMode;

    if (e && e.currentTarget && e.currentTarget.dataset) {
      // 从按钮点击获取模式
      newMode = e.currentTarget.dataset.mode;
    } else {
      // 从菜单切换，自动切换到另一种模式
      newMode = this.data.displayMode === 'timeline' ? 'waterfall' : 'timeline';
    }

    if (newMode === this.data.displayMode) return;

    console.log(`🔄 切换显示模式: ${this.data.displayMode} → ${newMode}`);

    // 🎬 添加切换动画
    this.animateDisplayModeSwitch(this.data.displayMode, newMode);

    this.setData({
      displayMode: newMode,
      showModeMenu: false,
      showMoreMenu: false
    });

    // 根据新模式重新处理数据
    if (this.data.currentTab === 'all' && this.data.albumImages.length > 0) {
      if (newMode === 'timeline') {
        this.groupImagesByDate();
      }
      // 瀑布流模式不需要额外处理
    }

    // 显示切换提示
    showSuccess(this, `已切换到${newMode === 'timeline' ? '时间轴' : '瀑布流'}模式`, 1500);
  },

  /**
   * 从菜单切换显示模式
   * 专门用于处理菜单中的显示模式切换
   */
  switchDisplayModeFromMenu: function() {
    // 调用通用的切换方法
    this.switchDisplayMode();
  },

  /**
   * 按日期分组图片（时间轴模式）
   * 将图片按创建日期分组，用于时间轴显示
   */
  groupImagesByDate: function() {
    const { albumImages } = this.data;

    if (!albumImages || albumImages.length === 0) {
      this.setData({ groupedImages: [] });
      return;
    }

    const grouped = {};
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

    albumImages.forEach(image => {
      const date = new Date(image.createTime);
      const dateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

      if (!grouped[dateKey]) {
        grouped[dateKey] = {
          date: dateKey,
          displayDate: this.formatDisplayDate(date, today, yesterday),
          images: []
        };
      }

      grouped[dateKey].images.push(image);
    });

    // 转换为数组并按日期倒序排列（最新的在前）
    const groupedArray = Object.values(grouped).sort((a, b) => {
      return new Date(b.date) - new Date(a.date);
    });

    this.setData({ groupedImages: groupedArray });
  },

  /**
   * 格式化显示日期
   * @param {Date} date - 要格式化的日期
   * @param {Date} today - 今天的日期（可选，用于优化）
   * @param {Date} yesterday - 昨天的日期（可选，用于优化）
   * @returns {string} 格式化后的日期字符串
   */
  formatDisplayDate: function(date, today, yesterday) {
    // 如果没有传入today和yesterday，则创建新的
    if (!today) {
      today = new Date();
    }
    if (!yesterday) {
      yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    }

    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    // 判断是否为今天或昨天
    if (date.toDateString() === today.toDateString()) {
      return '今天';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return '昨天';
    } else {
      return `${year}年${month}月${day}日`;
    }
  },

  // 🚀 极速标签页切换 - 立即响应，后台加载
  onTabChange: function(e) {
    const tab = e.currentTarget.dataset.tab;

    // 🔧 修复：处理从文件夹内部点击导航栏的情况
    const isInFolder = this.data.currentFolder !== null;
    const isSameTab = tab === this.data.currentTab;

    // 如果是同一个标签页但在文件夹内部，需要返回到标签页的根视图
    if (isSameTab && !isInFolder) {
      return; // 真正的同标签页且不在文件夹内，无需操作
    }

    // 🚀 性能优化：立即更新UI状态，保留有效数据
    const updateData = {
      currentTab: tab,
      currentFolder: null, // 🔧 修复：总是清空currentFolder，确保返回根视图
      selectedImages: [],
      isSelectionMode: false,
      showMoreMenu: false,
      showModeMenu: false
    };

    // 🔧 优化：不清空数据，避免界面闪烁
    // 如果切换到文件夹标签页，重置显示模式
    if (tab === 'folders') {
      updateData.displayMode = 'waterfall';
      // 只有在没有文件夹数据时才显示loading
      if (this.data.folders.length === 0) {
        updateData.loading = true;
      }
    } else {
      // 只有在没有图片数据时才显示loading
      if (this.data.albumImages.length === 0) {
        updateData.loading = true;
      }
    }

    this.setData(updateData);

    // 🚀 智能数据加载：根据情况决定加载策略
    if (tab === 'folders') {
      // 切换到文件夹标签页
      if (isSameTab && isInFolder) {
        // 🔧 修复：从文件夹内部点击"文件夹"按钮，返回文件夹列表
        if (this.data.folders.length === 0) {
          this.loadFolders();
        } else {
          this.refreshFolderDetails();
        }
      } else if (this.data.folders.length === 0) {
        // 没有文件夹数据时才加载
        this.loadFolders();
      } else {
        // 有数据时，后台刷新预览图和数量
        this.refreshFolderDetails();
      }
    } else {
      // 切换到全部图片标签页
      if (isInFolder) {
        // 🔧 修复：从文件夹内部切换到"全部图片"，必须重新加载全部图片
        this.loadAlbumImages(true);
      } else if (this.data.albumImages.length === 0) {
        // 没有图片数据时才重新加载
        this.loadAlbumImages(true);
      } else {
        // 有数据时，重新分组（如果是时间轴模式）
        if (this.data.displayMode === 'timeline') {
          this.groupImagesByDate();
        }
        // 清除loading状态
        this.setData({ loading: false });
      }
    }
  },







  /**
   * 加载文件夹列表
   * 性能优化：立即显示基本信息，后台加载详细信息
   * 竞争条件保护：使用版本号避免并发加载冲突
   */
  loadFolders: async function() {
    // 🔧 防止竞争条件：如果已经在加载，直接返回
    if (this.data.isLoadingFolders) {
      console.log('⚠️ 文件夹正在加载中，跳过重复请求');
      return;
    }

    // 🔧 设置加载状态和版本号
    const currentVersion = this.data.folderLoadingVersion + 1;
    this.setData({
      isLoadingFolders: true,
      folderLoadingVersion: currentVersion
    });

    try {
      const result = await getAllFolders();

      if (result.success) {
        const hasCustomFolders = result.data.some(folder => folder.type === 'custom');



        // 🚀 性能优化：立即显示基本文件夹信息，提供快速响应
        this.setData({
          folders: result.data.map(folder => ({
            ...folder,
            imageCount: folder.imageCount || -1, // -1表示正在加载
            previewImages: folder.previewImages || null // null表示正在加载
          })),
          hasCustomFolders,
          loading: false
        });

        // 🔄 后台异步加载详细信息（预览图和数量）
        this.loadCompletefolderInfo(result.data, currentVersion).then(() => {
          // 🔧 检查版本号，避免竞争条件
          if (this.data.folderLoadingVersion === currentVersion) {
            // 更新完整信息
            this.setData({ folders: result.data });
          } else {
            console.log('⚠️ 文件夹加载版本已过期，跳过更新');
          }
        }).catch(error => {
          console.warn('后台加载文件夹详情失败:', error);
        });

      } else {
        // 如果加载失败，尝试直接查询数据库
        const directResult = await db.collection('album_folders').get();

        if (directResult.data) {
          const hasCustomFolders = directResult.data.some(folder => folder.type === 'custom');



          // 🚀 立即显示基本信息
          this.setData({
            folders: directResult.data.map(folder => ({
              ...folder,
              imageCount: folder.imageCount || -1,
              previewImages: folder.previewImages || null
            })),
            hasCustomFolders,
            loading: false
          });

          // 🔄 后台加载详细信息
          this.loadCompletefolderInfo(directResult.data).then(() => {
            this.setData({ folders: directResult.data });
          }).catch(error => {
            console.warn('后台加载文件夹详情失败:', error);
          });

        } else {
          // 如果直接查询也失败，清除加载状态
          this.setData({
            folders: [],
            hasCustomFolders: false,
            loading: false
          });
        }
      }
    } catch (error) {
      console.error('加载文件夹异常:', error);
      // 如果出错，设置空数组并清除加载状态
      this.setData({
        folders: [],
        hasCustomFolders: false,
        loading: false
      });
    } finally {
      // 🔧 清除加载状态
      this.setData({ isLoadingFolders: false });
    }
  },

  /**
   * 刷新文件夹详情
   * 后台刷新预览图和数量，不阻塞UI
   */
  refreshFolderDetails: async function() {
    try {
      const folders = this.data.folders;
      if (!folders || folders.length === 0) return;

      // 后台异步更新文件夹详情
      await this.loadCompletefolderInfo(folders);

      // 更新文件夹数据
      this.setData({
        folders: folders,
        loading: false
      });
    } catch (error) {
      console.warn('刷新文件夹详情失败:', error);
      this.setData({ loading: false });
    }
  },

  /**
   * 加载完整的文件夹信息
   * 修复：确保预览图和数量都加载完成再显示
   * @param {Array} folders - 文件夹数组
   * @param {number} version - 加载版本号，用于避免竞争条件
   */
  loadCompletefolderInfo: async function(folders, version = null) {
    try {
      // 为每个文件夹设置初始状态
      folders.forEach(folder => {
        if (folder.imageCount === undefined) {
          folder.imageCount = -1; // -1 表示正在加载
        }
        if (!folder.previewImages) {
          folder.previewImages = null; // null 表示正在加载
        }
      });

      // 并行加载数量和预览图片
      await Promise.all([
        this.updateAllFolderCounts(folders),
        this.loadFolderPreviewImages(folders)
      ]);
    } catch (error) {
      console.warn('加载文件夹完整信息失败:', error);
      // 即使失败也设置默认值，避免无限加载
      folders.forEach(folder => {
        if (folder.imageCount === -1) {
          folder.imageCount = 0;
        }
        if (folder.previewImages === null) {
          folder.previewImages = [];
        }
      });
    }
  },



  /**
   * 后台更新文件夹详细信息
   * 异步更新文件夹数量和预览图片，不阻塞UI
   * @param {Array} folders - 文件夹数组
   */
  updateFolderDetailsInBackground: async function(folders) {
    try {
      // 并行执行数量更新和预览图片加载
      const [countsResult, previewsResult] = await Promise.allSettled([
        this.updateAllFolderCounts(folders),
        this.loadFolderPreviewImages(folders)
      ]);

      // 检查结果并记录错误（但不影响用户体验）
      if (countsResult.status === 'rejected') {
        console.warn('文件夹数量更新失败:', countsResult.reason);
      }
      if (previewsResult.status === 'rejected') {
        console.warn('预览图片加载失败:', previewsResult.reason);
      }

      // 更新UI（如果页面还在文件夹标签页）
      if (this.data.currentTab === 'folders') {
        this.setData({ folders });
      }

    } catch (error) {
      console.warn('后台更新文件夹详细信息失败:', error);
      // 不显示错误给用户，因为基本功能已经可用
    }
  },





  // 加载文件夹预览图片 - 优化版：批量查询，解决N+1问题
  loadFolderPreviewImages: async function(folders) {
    try {
      console.log('🚀 开始批量加载文件夹预览图，避免N+1查询问题');

      // 🔧 优化：使用批量查询替代N+1查询
      const [
        allImages,           // 所有图片（用于自定义文件夹）
        favoriteImages,      // 收藏图片
        bannerImages,        // 首页展示图片
        deletedImages        // 回收站图片
      ] = await Promise.all([
        // 查询所有未删除的图片（包含临时URL）
        db.collection('album_images').where({
          isDeleted: db.command.neq(true)
        }).orderBy('createTime', 'desc').limit(200).get(),

        // 查询收藏图片
        db.collection('album_images').where({
          isFavorite: true,
          isDeleted: db.command.neq(true)
        }).orderBy('createTime', 'desc').limit(20).get(),

        // 查询首页展示图片
        db.collection('album_images').where({
          bannerOrder: db.command.neq(null),
          isDeleted: db.command.neq(true)
        }).orderBy('bannerOrder', 'asc').limit(20).get(),

        // 查询回收站图片
        db.collection('album_images').where({
          isDeleted: true
        }).orderBy('createTime', 'desc').limit(20).get()
      ]);

      // 🔧 批量生成预览图URL
      const allImageUrls = await Promise.all([
        this.batchGetTempFileURLs(allImages.data),
        this.batchGetTempFileURLs(favoriteImages.data),
        this.batchGetTempFileURLs(bannerImages.data),
        this.batchGetTempFileURLs(deletedImages.data)
      ]);

      // 🔧 为每个文件夹分配预览图
      folders.forEach(folder => {
        let images = [];

        if (folder.systemType === 'favorite') {
          images = favoriteImages.data;
        } else if (folder.systemType === 'banner') {
          images = bannerImages.data;
        } else if (folder.systemType === 'trash') {
          images = deletedImages.data;
        } else if (folder.type === 'custom') {
          // 自定义文件夹：筛选包含该文件夹ID的图片
          images = allImages.data.filter(img =>
            (img.folderIds || []).includes(folder._id)
          ).slice(0, 4); // 最多4张预览图
        }

        // 获取对应的临时URL
        folder.previewImages = images.map(img => img.tempFileURL).filter(url => url);
      });

      console.log('✅ 批量加载文件夹预览图完成，避免了N+1查询');
    } catch (error) {
      console.error('批量加载文件夹预览图失败:', error);
      // 降级到单个查询
      console.log('🔄 降级到单个查询模式');
      for (let folder of folders) {
        try {
          const previewImages = await this.getFolderPreviewImages(folder);
          folder.previewImages = previewImages;
        } catch (singleError) {
          console.warn(`文件夹 ${folder.name} 预览图加载失败:`, singleError);
          folder.previewImages = [];
        }
      }
    }
  },

  /**
   * 批量获取临时文件URL
   * @param {Array} images - 图片数组
   * @returns {Array} 包含临时URL的图片数组
   */
  batchGetTempFileURLs: async function(images) {
    if (!images || images.length === 0) return [];

    try {
      const fileIDs = images.map(img => img.fileID);
      const result = await wx.cloud.getTempFileURL({
        fileList: fileIDs
      });

      // 将临时URL添加到图片对象中
      result.fileList.forEach((file, index) => {
        if (file.status === 0 && images[index]) {
          images[index].tempFileURL = file.tempFileURL;
        }
      });

      return images;
    } catch (error) {
      console.warn('批量获取临时URL失败:', error);
      return images;
    }
  },

  // 获取单个文件夹的预览图片
  getFolderPreviewImages: async function(folder) {
    try {
      let query;
      let orderBy = 'createTime';
      let orderDirection = 'desc';

      // 根据文件夹类型构建查询条件
      if (folder.systemType === 'favorite') {
        query = db.collection('album_images').where({
          isFavorite: true,
          isDeleted: db.command.neq(true) // 兼容旧数据
        });
      } else if (folder.systemType === 'banner') {
        query = db.collection('album_images').where({
          bannerOrder: db.command.neq(null),
          isDeleted: db.command.neq(true) // 兼容旧数据
        });
        orderBy = 'bannerOrder';
        orderDirection = 'asc';
      } else if (folder.systemType === 'trash') {
        query = db.collection('album_images').where({
          isDeleted: true
        });
        orderBy = 'deleteTime';
      } else if (folder.type === 'custom') {
        query = db.collection('album_images').where({
          folderIds: db.command.in([folder._id]),
          isDeleted: db.command.neq(true) // 兼容旧数据
        });
      } else {
        // 默认查询所有未删除图片
        query = db.collection('album_images').where({
          isDeleted: db.command.neq(true) // 兼容旧数据
        });
      }

      // 获取前4张图片作为预览
      const result = await query
        .limit(4)
        .orderBy(orderBy, orderDirection)
        .get();

      if (result.data && result.data.length > 0) {
        // 获取fileID列表
        const fileList = result.data
          .map(img => img.fileID)
          .filter(fileID => fileID && fileID.trim() !== '');

        if (fileList.length === 0) {
          return [];
        }

        // 获取临时访问链接
        try {
          const urlResult = await wx.cloud.getTempFileURL({
            fileList: fileList
          });

          const validImages = urlResult.fileList
            .map(file => file.tempFileURL)
            .filter(url => url && url.trim() !== '');

          return validImages;
        } catch (urlError) {
          console.error('获取临时URL失败:', urlError);
          return [];
        }
      }

      return [];
    } catch (error) {
      console.error('获取文件夹预览图片失败:', error);
      return [];
    }
  },

  // 选择文件夹
  onSelectFolder: function(e) {
    const folderId = e.currentTarget.dataset.folderId;
    const folder = this.data.folders.find(f => f._id === folderId);



    if (!folder) {
      console.error('🚨 找不到文件夹:', folderId);
      return;
    }

    console.log('📁 进入文件夹:', folder.name, '类型:', folder.type, 'systemType:', folder.systemType);

    this.setData({
      currentFolder: folder,
      selectedImages: [],
      isSelectionMode: false
    });

    // 重新加载该文件夹的图片
    this.loadAlbumImages(true);
  },

  // 返回文件夹列表
  onBackToFolders: function() {
    this.setData({
      currentFolder: null,
      selectedImages: [],
      isSelectionMode: false
    });

    // 重新加载文件夹列表
    this.loadFolders();
  },

  // 删除当前文件夹（从右上角菜单触发）
  onDeleteCurrentFolder: function() {
    const { currentFolder } = this.data;

    if (!currentFolder || currentFolder.type !== 'custom') {
      showError(this, '只能删除自定义文件夹');
      return;
    }

    this.setData({ showMoreMenu: false });

    // 显示删除确认对话框
    wx.showModal({
      title: '删除文件夹',
      content: `确定要删除文件夹"${currentFolder.name}"吗？\n\n⚠️ 此操作不可恢复\n📁 文件夹中的图片不会被删除`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteCustomFolder(currentFolder._id, currentFolder.name);
        }
      }
    });
  },





  // 图片点击事件
  onImageTap: function(e) {
    const image = e.currentTarget.dataset.image;

    if (this.data.isSelectionMode) {
      // 批量选择模式：切换选择状态
      this.toggleImageSelection(image._id);
    } else {
      // 普通模式：预览图片
      this.previewImage(image);
    }
  },

  // 图片长按事件 - 进入多选模式或开始拖拽
  onImageLongPress: function(e) {
    // 如果在排序模式下，不响应长按事件（拖拽模式下由onDragStart处理）
    if (this.data.isSortMode) {
      return;
    }

    const image = e.currentTarget.dataset.image;

    // 进入批量选择模式并选中当前图片
    this.setData({
      isSelectionMode: true,
      selectedImages: [image._id]
    });

    // 更新图片的选中状态
    this.updateImageSelectionState();

    // 提供触觉反馈
    wx.vibrateShort();
  },

  // 拖拽开始（排序模式下的长按）
  onDragStart: function(e) {
    if (!this.data.isSortMode || this.data.showSortButtons) return;

    const index = e.currentTarget.dataset.index;
    const touch = e.touches[0];

    this.setData({
      dragStartIndex: index,
      isDragging: true,
      touchStartX: touch.clientX,
      touchStartY: touch.clientY,
      currentTouchX: touch.clientX,
      currentTouchY: touch.clientY,
      dragOffsetX: 0,
      dragOffsetY: 0
    });

    // 提供触觉反馈
    wx.vibrateShort();
  },

  // 触摸开始
  onTouchStart: function(e) {
    if (!this.data.isSortMode || this.data.showSortButtons) return;

    const touch = e.touches[0];
    this.setData({
      touchStartTime: Date.now(),
      touchStartX: touch.clientX,
      touchStartY: touch.clientY,
      currentTouchX: touch.clientX,
      currentTouchY: touch.clientY
    });
  },

  // 触摸移动 - 实时拖拽效果
  onTouchMove: function(e) {
    if (!this.data.isDragging || this.data.showSortButtons) return;

    // 阻止默认滚动行为
    e.preventDefault && e.preventDefault();

    const now = Date.now();
    const { lastMoveTime } = this.data;

    // 节流处理，每16ms（约60fps）更新一次
    if (now - lastMoveTime < 16) return;

    const touch = e.touches[0];
    const { touchStartX, touchStartY } = this.data;

    // 计算拖拽偏移
    const dragOffsetX = touch.clientX - touchStartX;
    const dragOffsetY = touch.clientY - touchStartY;

    this.setData({
      currentTouchX: touch.clientX,
      currentTouchY: touch.clientY,
      dragOffsetX: dragOffsetX,
      dragOffsetY: dragOffsetY,
      lastMoveTime: now
    });

    // 计算目标位置并实时更新
    this.calculateDropTarget(touch.clientX, touch.clientY);
  },

  // 触摸结束 - 完成拖拽排序
  onTouchEnd: function(e) {
    if (!this.data.isDragging || this.data.showSortButtons) return;

    const { dragStartIndex, dragOverIndex } = this.data;

    // 如果有有效的拖拽操作
    if (dragStartIndex !== -1 && dragOverIndex !== -1 && dragStartIndex !== dragOverIndex) {
      this.performDragSort(dragStartIndex, dragOverIndex);
    }

    // 重置拖拽状态
    this.setData({
      dragStartIndex: -1,
      dragOverIndex: -1,
      isDragging: false,
      dragOffsetX: 0,
      dragOffsetY: 0
    });
  },

  // 触摸取消
  onTouchCancel: function(e) {
    this.setData({
      dragStartIndex: -1,
      dragOverIndex: -1,
      isDragging: false,
      dragOffsetX: 0,
      dragOffsetY: 0
    });
  },

  // 计算拖拽目标位置
  calculateDropTarget: function(clientX, clientY) {
    const query = wx.createSelectorQuery().in(this);

    query.selectAll('.image-item').boundingClientRect((rects) => {
      let targetIndex = -1;

      // 找到触摸点所在的图片区域
      rects.forEach((rect, index) => {
        if (clientX >= rect.left && clientX <= rect.right &&
            clientY >= rect.top && clientY <= rect.bottom) {
          targetIndex = index;
        }
      });

      // 如果没有直接命中，找最近的
      if (targetIndex === -1) {
        let minDistance = Infinity;
        rects.forEach((rect, index) => {
          const centerX = rect.left + rect.width / 2;
          const centerY = rect.top + rect.height / 2;
          const distance = Math.sqrt(
            Math.pow(clientX - centerX, 2) + Math.pow(clientY - centerY, 2)
          );

          if (distance < minDistance) {
            minDistance = distance;
            targetIndex = index;
          }
        });
      }

      // 更新拖拽目标
      if (targetIndex !== -1 && targetIndex !== this.data.dragOverIndex) {
        this.setData({
          dragOverIndex: targetIndex
        });

        // 实时预览排序效果
        this.previewSort(this.data.dragStartIndex, targetIndex);
      }
    }).exec();
  },

  // 预览排序效果（不保存到数据库）
  previewSort: function(fromIndex, toIndex) {
    if (fromIndex === toIndex) return;

    const albumImages = [...this.data.albumImages];

    // 移动元素
    const [movedItem] = albumImages.splice(fromIndex, 1);
    albumImages.splice(toIndex, 0, movedItem);

    // 更新显示，但不触发数据库保存
    this.setData({
      albumImages: albumImages,
      dragStartIndex: toIndex // 更新拖拽起始索引
    });
  },

  // 执行拖拽排序
  performDragSort: function(fromIndex, toIndex) {
    // 由于已经在previewSort中实时更新了，这里只需要提供反馈
    // 提供触觉反馈
    wx.vibrateShort();

    // 显示成功提示
    showToast(this, {
      message: `已移动到第${toIndex + 1}位`,
      theme: 'success',
      duration: 1000
    });
  },

  // 回收站图片长按事件 - 同样进入多选模式
  onTrashImageLongPress: function(e) {
    this.onImageLongPress(e);
  },

  // 切换图片选择状态
  toggleImageSelection: function(imageId) {
    const { selectedImages } = this.data;
    const index = selectedImages.indexOf(imageId);

    let newSelectedImages;
    if (index > -1) {
      // 取消选择
      newSelectedImages = selectedImages.filter(id => id !== imageId);
    } else {
      // 添加选择
      newSelectedImages = [...selectedImages, imageId];
    }

    this.setData({
      selectedImages: newSelectedImages
    });

    // 更新图片选中状态显示
    this.updateImageSelectionState();

    // 如果没有选中的图片，退出选择模式
    if (newSelectedImages.length === 0) {
      this.setData({ isSelectionMode: false });
    }
  },

  // 更新图片选中状态显示
  updateImageSelectionState: function() {
    const { selectedImages, albumImages, groupedImages } = this.data;

    // 更新 albumImages 中的选中状态
    const updatedAlbumImages = albumImages.map(image => ({
      ...image,
      selected: selectedImages.includes(image._id)
    }));

    // 更新 groupedImages 中的选中状态
    const updatedGroupedImages = groupedImages.map(group => ({
      ...group,
      images: group.images.map(image => ({
        ...image,
        selected: selectedImages.includes(image._id)
      }))
    }));

    this.setData({
      albumImages: updatedAlbumImages,
      groupedImages: updatedGroupedImages
    });
  },

  // 预览图片
  previewImage: function(image) {
    const { albumImages } = this.data;
    const urls = albumImages.map(img => img.tempFileURL).filter(url => url);
    const current = image.tempFileURL;

    wx.previewImage({
      current,
      urls
    });
  },

  // 右上角更多菜单切换
  onToggleMoreMenu: function() {
    this.setData({
      showMoreMenu: !this.data.showMoreMenu
    });
  },

  // 更多菜单状态变化
  onMoreMenuChange: function(e) {
    this.setData({
      showMoreMenu: false
    });
  },

  // 阻止菜单内部点击事件冒泡
  onMenuStopPropagation: function(e) {
    // 阻止事件冒泡，防止点击菜单内容时关闭菜单
  },

  // 菜单操作处理（处理右上角菜单中的单张图片操作）
  onMenuAction: async function(e) {
    const action = e.currentTarget.dataset.action;
    const { selectedImages } = this.data;

    if (selectedImages.length !== 1) {
      showError(this, '请选择一张图片');
      return;
    }

    this.setData({ showMoreMenu: false });

    const imageId = selectedImages[0];
    const image = this.data.albumImages.find(img => img._id === imageId);

    if (!image) {
      showError(this, '图片不存在');
      return;
    }

    try {
      switch (action) {
        case 'info':
          await this.showImageInfo(imageId);
          break;
        case 'favorite':
          await this.toggleFavorite(imageId);
          break;
        case 'move':
          this.showFolderSelector(imageId);
          break;
        case 'banner':
          await this.toggleBanner(image);
          break;
        case 'delete':
          if (this.data.currentFolder && this.data.currentFolder.systemType === 'trash') {
            await this.permanentDeleteImage(imageId);
          } else {
            await this.deleteImage(imageId);
          }
          break;
        case 'restore':
          await this.restoreImage(imageId);
          break;
        case 'remove':
          await this.removeImageFromCurrentFolder(imageId);
          break;
      }
    } catch (error) {
      console.error('菜单操作失败:', error);
      showError(this, '操作失败');
    }
  },

  // 创建文件夹
  onCreateFolder: function() {
    this.setData({ showMoreMenu: false });

    wx.showModal({
      title: '新建文件夹',
      editable: true,
      placeholderText: '请输入文件夹名称',
      success: async (res) => {
        if (res.confirm && res.content) {
          const result = await createCustomFolder(res.content);

          if (result.success) {
            showToast(this, { message: '创建成功', theme: 'success' });
            this.loadFolders(); // 重新加载文件夹列表
          } else {
            showError(this, result.message);
          }
        }
      }
    });
  },



  // 进入排序模式
  onEnterSortMode: function() {
    const { albumImages } = this.data;

    // 保存原始排序
    const originalOrder = albumImages.map(img => ({
      _id: img._id,
      bannerOrder: img.bannerOrder
    }));

    this.setData({
      showMoreMenu: false,
      isSortMode: true,
      isSelectionMode: false, // 确保退出选择模式
      selectedImages: [], // 清空选择的图片
      originalOrder: originalOrder,
      showSortButtons: false, // 默认使用拖拽模式
      dragStartIndex: -1,
      dragOverIndex: -1,
      isDragging: false
    });

    showToast(this, { message: '长按图片拖拽排序，实时预览效果', theme: 'info' });
  },

  // 切换排序模式（拖拽 vs 按钮）
  onToggleSortMode: function() {
    const showSortButtons = !this.data.showSortButtons;
    this.setData({
      showSortButtons,
      dragStartIndex: -1,
      dragOverIndex: -1,
      isDragging: false,
      dragOffsetX: 0,
      dragOffsetY: 0,
      lastMoveTime: 0
    });

    const message = showSortButtons ? '点击箭头按钮调整顺序' : '长按图片拖拽排序';
    showToast(this, { message, theme: 'info' });
  },

  // 批量选择
  onBatchSelect: function() {
    this.setData({
      showMoreMenu: false,
      isSelectionMode: true,
      selectedImages: []
    });

    // 更新图片选中状态显示
    this.updateImageSelectionState();
  },

  // 清空回收站
  onEmptyTrash: async function() {
    this.setData({ showMoreMenu: false });

    wx.showModal({
      title: '清空回收站',
      content: '确定要永久删除回收站中的所有图片吗？此操作不可恢复。',
      confirmText: '清空',
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          showLoading(this, '清空中...');
          const result = await emptyTrash();
          hideToast(this);

          if (result.success) {
            showToast(this, { message: result.message, theme: 'success' });
            // 智能更新：清空回收站UI中的所有图片
            this.setData({ albumImages: [] });
            // 更新回收站数量为0
            this.updateSingleFolderCount('folder_trash', 'system', 'trash');
          } else {
            showError(this, result.message);
          }
        }
      }
    });
  },



  // 切换收藏状态（优化版：智能局部更新）
  toggleFavorite: async function(imageId) {
    showLoading(this, '处理中...');

    try {
      // 1. 立即更新UI状态，提供即时反馈
      const image = this.data.albumImages.find(img => img._id === imageId);
      if (image) {
        this.updateImageInList(imageId, {
          isFavorite: !image.isFavorite
        });
      }

      // 2. 后台同步到服务器（带重试机制）
      const result = await this.retryRequest(
        () => toggleImageFavorite(imageId),
        3,
        '切换收藏状态'
      );
      hideToast(this);

      if (result.success) {
        showToast(this, { message: result.message, theme: 'success' });
        // 3. 只更新收藏夹数量，不重新加载所有数据
        this.updateSingleFolderCount('folder_favorite', 'system', 'favorite');
      } else {
        // 如果服务器操作失败，回滚UI状态
        this.updateImageInList(imageId, {
          isFavorite: image.isFavorite
        });
        showError(this, result.message);
      }
    } catch (error) {
      hideToast(this);
      console.error('切换收藏状态失败:', error);
      showError(this, '操作失败，请重试');
      // 发生错误时重新加载数据确保一致性
      this.loadAlbumImages(true);
    }
  },

  // 显示文件夹选择器
  showFolderSelector: async function(imageId) {
    try {
      // 🚀 性能优化：优先使用已有数据，避免不必要的重新加载
      let folders = this.data.folders;

      // 只有在没有文件夹数据时才进行快速加载
      if (!folders || folders.length === 0) {
        const result = await getAllFolders();
        if (result.success) {
          folders = result.data;
          this.data.folders = folders;
        }
      }

      // 检查是否缺少系统文件夹
      const hasSystemFolders = folders.some(f => f.type === 'system');
      if (!hasSystemFolders || folders.length === 0) {
        // 直接在这里创建系统文件夹，不依赖其他方法
        const systemFolders = [
          {
            id: 'folder_favorite',
            name: '收藏夹',
            type: 'system',
            systemType: 'favorite',
            createTime: new Date(),
            updateTime: new Date(),
            imageCount: 0
          },
          {
            id: 'folder_banner',
            name: '首页展示',
            type: 'system',
            systemType: 'banner',
            createTime: new Date(),
            updateTime: new Date(),
            imageCount: 0
          },
          {
            id: 'folder_trash',
            name: '回收站',
            type: 'system',
            systemType: 'trash',
            createTime: new Date(),
            updateTime: new Date(),
            imageCount: 0
          }
        ];

        for (const folder of systemFolders) {
          try {
            // 不在data中包含_id，而是通过doc()指定
            const { id, ...folderData } = folder;
            await db.collection('album_folders').doc(id).set({
              data: folderData
            });
          } catch (error) {
            if (error.errCode !== -502002) { // 不是"文档已存在"错误
              throw error;
            }
          }
        }

        // 重新获取文件夹数据
        const reloadResult = await getAllFolders();
        if (reloadResult.success) {
          folders = reloadResult.data;
          this.data.folders = folders;
        }
      }

      // 如果还是没有文件夹，设置一个临时的
      if (folders.length === 0) {
        folders = [
          {
            _id: 'folder_favorite',
            name: '收藏夹',
            type: 'system',
            systemType: 'favorite',
            imageCount: 0
          },
          {
            _id: 'folder_banner',
            name: '首页展示',
            type: 'system',
            systemType: 'banner',
            imageCount: 0
          }
        ];
        this.data.folders = folders;
      }

    } catch (error) {
      console.error('准备文件夹选择器时出错:', error);
      // 设置默认文件夹
      folders = [
        {
          _id: 'folder_favorite',
          name: '收藏夹',
          type: 'system',
          systemType: 'favorite',
          imageCount: 0
        }
      ];
      this.data.folders = folders;
    }

    // 获取当前图片的文件夹信息
    let currentImageFolders = [];
    if (imageId) {
      const image = this.data.albumImages.find(img => img._id === imageId);
      if (image) {
        currentImageFolders = this.getCurrentImageFolders(image);
      }
    }

    // 🚀 立即显示对话框，使用当前的文件夹数据
    this.setData({
      showFolderDialog: true,
      selectedImageId: imageId, // 如果是批量操作，这里会是null
      selectedFolderIds: [...currentImageFolders], // 复制当前文件夹状态
      currentImageFolders: currentImageFolders,
      folders: folders // 确保使用最新的文件夹数据
    });
  },

  // 显示归类对话框（显示系统文件夹和自定义文件夹，但排除回收站）
  showClassifyDialog: async function() {
    try {
      // 🚀 性能优化：直接使用已有的文件夹数据，无需重新加载
      let folders = this.data.folders;

      // 如果没有文件夹数据，才进行快速加载
      if (!folders || folders.length === 0) {
        const result = await getAllFolders();
        if (result.success) {
          folders = result.data;
          // 🔧 修复：使用setData正确更新数据，避免数据不一致
          this.setData({ folders: folders });
        } else {
          showError(this, '获取文件夹列表失败');
          return;
        }
      }

      // 过滤出可用于归类的文件夹：
      // 1. 自定义文件夹（type === 'custom'）
      // 2. 系统文件夹，但排除回收站（type === 'system' && systemType !== 'trash'）
      const availableFolders = folders.filter(folder =>
        folder.type === 'custom' ||
        (folder.type === 'system' && folder.systemType !== 'trash')
      );

      if (availableFolders.length === 0) {
        showToast(this, { message: '暂无可用文件夹，请先创建自定义文件夹', theme: 'warning' });
        return;
      }

      // 🚀 获取选中图片的当前文件夹归属
      const { selectedImages } = this.data;
      let currentFolderIds = [];

      if (selectedImages && selectedImages.length > 0) {
        // 批量操作：获取所有选中图片共同的文件夹
        const allImageFolders = selectedImages.map(imageId => {
          const image = this.data.albumImages.find(img => img._id === imageId);
          if (image) {
            // 返回可用文件夹ID（自定义文件夹 + 系统文件夹但排除回收站）
            const folderIds = (image.folderIds || []).filter(folderId =>
              availableFolders.some(folder => folder._id === folderId)
            );

            // 添加系统文件夹的特殊处理
            if (image.isFavorite) {
              folderIds.push('folder_favorite');
            }
            if (image.bannerOrder !== null && image.bannerOrder !== undefined) {
              folderIds.push('folder_banner');
            }

            // 去重并返回
            return [...new Set(folderIds)];
          }
          return [];
        });

        // 🔧 修复：显示所有图片涉及的文件夹（并集），让用户明确看到当前状态
        if (allImageFolders.length > 0) {
          // 获取所有图片涉及的文件夹（并集）
          const allFolderIds = new Set();
          allImageFolders.forEach(imageFolders => {
            imageFolders.forEach(folderId => allFolderIds.add(folderId));
          });
          currentFolderIds = Array.from(allFolderIds);
        }
      }

      // 🔧 修复：为每个文件夹添加详细状态信息
      const foldersWithState = availableFolders.map(folder => {
        // 计算有多少张选中的图片在这个文件夹中
        let imageCountInFolder = 0;
        if (selectedImages && selectedImages.length > 0) {
          imageCountInFolder = selectedImages.filter(imageId => {
            const image = this.data.albumImages.find(img => img._id === imageId);
            if (!image) return false;

            // 检查图片是否在这个文件夹中
            if (folder._id === 'folder_favorite') {
              return image.isFavorite;
            } else if (folder._id === 'folder_banner') {
              return image.bannerOrder !== null && image.bannerOrder !== undefined;
            } else {
              return (image.folderIds || []).includes(folder._id);
            }
          }).length;
        }

        return {
          ...folder,
          isSelected: currentFolderIds.includes(folder._id),
          imageCountInFolder, // 有多少张选中的图片在这个文件夹中
          isPartial: imageCountInFolder > 0 && imageCountInFolder < selectedImages.length // 是否部分选中
        };
      });

      this.setData({
        showFolderDialog: true,
        selectedImageId: null, // 批量操作
        selectedFolderIds: [...currentFolderIds], // 预先选中当前文件夹
        currentImageFolders: currentFolderIds,
        // 🔧 修复：使用新的字段存储归类对话框的文件夹，避免覆盖主文件夹数据
        classifyFolders: foldersWithState // 归类对话框专用的文件夹数据
      });
    } catch (error) {
      console.error('显示归类对话框失败:', error);
      showError(this, '加载文件夹失败');
    }
  },

  // 切换文件夹选择状态（支持多选）
  onToggleDialogFolder: function(e) {
    const folderId = e.currentTarget.dataset.folderId;
    const { selectedFolderIds } = this.data;

    let newSelectedFolderIds;
    if (selectedFolderIds.includes(folderId)) {
      // 取消选择
      newSelectedFolderIds = selectedFolderIds.filter(id => id !== folderId);
    } else {
      // 添加选择
      newSelectedFolderIds = [...selectedFolderIds, folderId];

      // 回收站逻辑检查
      if (folderId === 'folder_trash') {
        // 如果选择回收站，清除其他所有文件夹
        newSelectedFolderIds = ['folder_trash'];
        showToast(this, { message: '回收站不能与其他文件夹同时选择', theme: 'warning' });
      } else if (newSelectedFolderIds.includes('folder_trash')) {
        // 如果已选择回收站，不能再选择其他文件夹
        newSelectedFolderIds = selectedFolderIds; // 保持原状态
        showToast(this, { message: '回收站不能与其他文件夹同时选择', theme: 'warning' });
        return;
      }
    }

    // 🔧 修复：同时更新classifyFolders和folders的选中状态
    // 更新归类对话框专用的文件夹数据（用于显示勾选效果）
    const updatedClassifyFolders = this.data.classifyFolders.map(folder => ({
      ...folder,
      isSelected: newSelectedFolderIds.includes(folder._id)
    }));

    // 同时更新主文件夹数据（保持数据一致性）
    const updatedFolders = this.data.folders.map(folder => ({
      ...folder,
      isSelected: newSelectedFolderIds.includes(folder._id)
    }));

    // 🔧 修复：确保hasCustomFolders状态正确
    const hasCustomFolders = updatedFolders.some(folder => folder.type === 'custom');

    this.setData({
      selectedFolderIds: newSelectedFolderIds,
      classifyFolders: updatedClassifyFolders, // 🔧 修复：更新归类对话框的文件夹数据
      folders: updatedFolders,
      hasCustomFolders: hasCustomFolders // 🔧 修复：确保自定义文件夹显示状态正确
    });
  },

  // 文件夹对话框确认
  onFolderDialogConfirm: async function() {
    const { selectedImageId, selectedImages, selectedFolderIds, currentImageFolders } = this.data;

    this.setData({
      showFolderDialog: false,
      classifyFolders: [] // 🔧 修复：清理归类对话框的文件夹数据
    });

    showLoading(this, '保存中...');

    try {
      if (selectedImageId) {
        // 单张图片操作
        await this.updateImageFolders(selectedImageId, selectedFolderIds, currentImageFolders);
        showToast(this, { message: '文件夹设置已保存', theme: 'success' });
      } else if (selectedImages && selectedImages.length > 0) {
        // 🔧 修复：批量操作 - 完全按照用户选择设置归类

        // 🚀 性能优化：并行执行批量归类，解决串行执行问题
        const updatePromises = selectedImages.map(async (imageId) => {
          try {
            // 获取当前图片的文件夹信息
            const image = this.data.albumImages.find(img => img._id === imageId);
            const currentFolders = image ? this.getCurrentImageFolders(image) : [];

            // 🔧 修复：完全按照用户选择设置归类
            // 用户选择什么，图片的归类就是什么（除了回收站状态需要保持）
            const isInTrash = currentFolders.includes('folder_trash');
            let newFolderIds = [...selectedFolderIds];

            // 如果图片在回收站中，保持回收站状态（因为回收站不在选择列表中）
            if (isInTrash) {
              newFolderIds.push('folder_trash');
            }



            return await this.updateImageFolders(imageId, newFolderIds, currentFolders);
          } catch (error) {
            console.error(`图片 ${imageId} 归类失败:`, error);
            throw error;
          }
        });

        // 🚀 并行执行所有更新操作
        const results = await Promise.allSettled(updatePromises);

        // 检查结果
        const failedCount = results.filter(result => result.status === 'rejected').length;
        const successCount = selectedImages.length - failedCount;

        if (failedCount === 0) {
          showToast(this, { message: `已为${selectedImages.length}张图片设置归类`, theme: 'success' });
        } else {
          showWarning(this, `${successCount}张设置成功，${failedCount}张设置失败`);
        }
        showToast(this, { message: `已为${selectedImages.length}张图片设置归类`, theme: 'success' });
        this.onCancelSelection(); // 退出选择模式
      }

      // 智能更新：只更新相关文件夹数量，不重新加载所有数据
      this.updateRelevantFolderCounts();
    } catch (error) {
      console.error('设置文件夹失败:', error);
      showError(this, '设置失败');
    } finally {
      hideToast(this);
    }
  },

  // 文件夹对话框取消
  onFolderDialogCancel: function() {
    // 🚀 立即关闭对话框，无需重新加载任何数据
    this.setData({
      showFolderDialog: false,
      selectedFolderIds: [],
      currentImageFolders: [],
      classifyFolders: [] // 🔧 修复：清理归类对话框的文件夹数据
    });
  },

  // 切换首页显示状态
  toggleBanner: async function(image) {
    const { maxBannerCount } = this.data;

    if (image.bannerOrder) {
      // 取消首页显示
      await this.updateBannerOrder(image._id, null);
    } else {
      // 设为首页显示
      // 先检查是否已达到上限
      const bannerCount = this.data.albumImages.filter(img => img.bannerOrder).length;
      if (bannerCount >= maxBannerCount) {
        showError(this, `最多只能选择${maxBannerCount}张首页图片`);
        return;
      }

      // 分配新的编号
      const newOrder = bannerCount + 1;
      await this.updateBannerOrder(image._id, newOrder);
    }
  },

  // 更新首页显示编号
  updateBannerOrder: async function(imageId, bannerOrder) {
    showLoading(this, '保存中...');

    try {
      await db.collection('album_images').doc(imageId).update({
        data: { bannerOrder }
      });

      hideToast(this);
      showToast(this, { message: '设置成功', theme: 'success' });

      // 智能更新：立即更新UI中的图片状态
      this.updateImageInList(imageId, { bannerOrder });

      // 只更新首页展示文件夹数量
      this.updateSingleFolderCount('folder_banner', 'system', 'banner');
    } catch (error) {
      hideToast(this);
      showError(this, '设置失败');
    }
  },

  // 删除图片（移入回收站）- 优化版：智能局部更新
  deleteImage: async function(imageId) {
    // 确保回收站文件夹存在
    await this.ensureTrashFolderExists();

    showLoading(this, '删除中...');

    try {
      // 1. 立即从UI中移除图片，提供即时反馈
      this.removeImagesFromList([imageId]);

      // 2. 后台移入回收站
      const result = await moveToTrash(imageId);
      hideToast(this);

      if (result.success) {
        showToast(this, { message: result.message, theme: 'success' });
        // 3. 只更新相关文件夹数量，不重新加载所有数据
        this.updateRelevantFolderCounts();
      } else {
        // 如果操作失败，重新加载数据恢复状态
        showError(this, result.message);
        this.loadAlbumImages(true);
      }
    } catch (error) {
      hideToast(this);
      console.error('删除图片失败:', error);
      showError(this, '删除失败，请重试');
      // 发生错误时重新加载数据确保一致性
      this.loadAlbumImages(true);
    }
  },

  // 永久删除图片
  permanentDeleteImage: async function(imageId) {
    wx.showModal({
      title: '永久删除',
      content: '确定要永久删除这张图片吗？此操作不可恢复。',
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          showLoading(this, '删除中...');
          const result = await permanentDelete(imageId);
          hideToast(this);

          if (result.success) {
            showToast(this, { message: result.message, theme: 'success' });
            // 智能更新：从UI中移除图片
            this.removeImagesFromList([imageId]);
            // 更新回收站数量
            this.updateSingleFolderCount('folder_trash', 'system', 'trash');
          } else {
            showError(this, result.message);
          }
        }
      }
    });
  },

  // 恢复图片
  restoreImage: async function(imageId) {
    showLoading(this, '恢复中...');
    const result = await restoreFromTrash(imageId);
    hideToast(this);

    if (result.success) {
      showToast(this, { message: result.message, theme: 'success' });
      // 智能更新：从回收站UI中移除图片
      this.removeImagesFromList([imageId]);
      // 更新相关文件夹数量
      this.updateRelevantFolderCounts();
    } else {
      showError(this, result.message);
    }
  },

  // 取消批量选择
  onCancelSelection: function() {
    this.setData({
      isSelectionMode: false,
      selectedImages: []
    });

    // 更新图片选中状态显示
    this.updateImageSelectionState();
  },

  // 批量操作
  onBatchAction: async function(e) {
    const action = e.currentTarget.dataset.action;
    const { selectedImages } = this.data;

    if (selectedImages.length === 0) {
      showError(this, '请先选择图片');
      return;
    }

    try {
      switch (action) {
        case 'favorite':
          await this.batchToggleFavorite();
          break;
        case 'classify':
          this.showClassifyDialog(); // 批量归类（显示系统文件夹和自定义文件夹，但排除回收站）
          break;
        case 'delete':
          await this.batchDelete();
          break;
        case 'remove':
          await this.batchRemoveFromCurrentFolder();
          break;
      }
    } catch (error) {
      console.error('批量操作失败:', error);
      showError(this, '操作失败');
    }
  },

  // 批量切换收藏 - 优化版：智能批量更新
  batchToggleFavorite: async function() {
    const { selectedImages } = this.data;
    showLoading(this, '处理中...');

    try {
      // 1. 立即更新UI状态，提供即时反馈
      const updates = {};
      selectedImages.forEach(imageId => {
        const image = this.data.albumImages.find(img => img._id === imageId);
        if (image) {
          updates[imageId] = !image.isFavorite;
          this.updateImageInList(imageId, { isFavorite: !image.isFavorite });
        }
      });

      // 2. 后台批量处理
      const promises = selectedImages.map(imageId => toggleImageFavorite(imageId));
      const results = await Promise.allSettled(promises);

      hideToast(this);

      // 3. 检查结果并处理失败的情况
      const failedCount = results.filter(result => result.status === 'rejected').length;

      if (failedCount === 0) {
        showToast(this, { message: `已处理${selectedImages.length}张图片`, theme: 'success' });
        // 4. 只更新收藏夹数量
        this.updateSingleFolderCount('folder_favorite', 'system', 'favorite');
      } else {
        showWarning(this, `${selectedImages.length - failedCount}张处理成功，${failedCount}张处理失败`);
        // 如果有失败，重新加载确保数据一致性
        this.loadAlbumImages(true);
      }

      this.onCancelSelection(); // 退出选择模式
    } catch (error) {
      hideToast(this);
      console.error('批量切换收藏失败:', error);
      showError(this, '操作失败，请重试');
      // 发生错误时重新加载数据
      this.loadAlbumImages(true);
    }
  },

  // 显示批量文件夹选择器
  showBatchFolderSelector: async function() {
    // 直接调用单张图片的文件夹选择器，传入null表示批量操作
    await this.showFolderSelector(null);
  },

  // 如果需要，创建系统文件夹
  createSystemFoldersIfNeeded: async function() {
    try {
      console.log('开始创建系统文件夹...');

      const systemFolders = [
        {
          id: 'folder_favorite',
          name: '收藏夹',
          type: 'system',
          systemType: 'favorite',
          createTime: new Date(),
          updateTime: new Date(),
          imageCount: 0
        },
        {
          id: 'folder_banner',
          name: '首页展示',
          type: 'system',
          systemType: 'banner',
          createTime: new Date(),
          updateTime: new Date(),
          imageCount: 0
        },
        {
          id: 'folder_trash',
          name: '回收站',
          type: 'system',
          systemType: 'trash',
          createTime: new Date(),
          updateTime: new Date(),
          imageCount: 0
        }
      ];

      // 批量创建系统文件夹
      for (const folder of systemFolders) {
        try {
          // 不在data中包含_id，而是通过doc()指定
          const { id, ...folderData } = folder;
          await db.collection('album_folders').doc(id).set({
            data: folderData
          });
          console.log(`创建系统文件夹成功: ${folder.name}`);
        } catch (error) {
          if (error.errCode === -502002) {
            // 文档已存在，跳过
            console.log(`系统文件夹已存在: ${folder.name}`);
          } else {
            console.error(`创建系统文件夹失败: ${folder.name}`, error);
          }
        }
      }

      console.log('系统文件夹创建完成');
    } catch (error) {
      console.error('创建系统文件夹异常:', error);
    }
  },

  // 批量删除
  batchDelete: async function() {
    const { selectedImages, currentFolder } = this.data;

    const isTrash = currentFolder && currentFolder.systemType === 'trash';
    const title = isTrash ? '永久删除' : '删除';
    const content = isTrash
      ? `确定要永久删除选中的${selectedImages.length}张图片吗？此操作不可恢复。`
      : `确定要删除选中的${selectedImages.length}张图片吗？`;

    wx.showModal({
      title,
      content,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          // 如果不是回收站操作，确保回收站文件夹存在
          if (!isTrash) {
            await this.ensureTrashFolderExists();
          }

          showLoading(this, '删除中...');

          try {
            // 1. 立即从UI中移除图片，提供即时反馈
            this.removeImagesFromList(selectedImages);

            // 2. 后台执行删除操作
            let result;
            if (isTrash) {
              result = await permanentDelete(selectedImages);
            } else {
              result = await moveToTrash(selectedImages);
            }

            hideToast(this);

            if (result.success) {
              showToast(this, { message: result.message, theme: 'success' });
              this.onCancelSelection(); // 退出选择模式
              // 3. 只更新相关文件夹数量，不重新加载所有数据
              this.updateRelevantFolderCounts();
            } else {
              showError(this, result.message);
              // 如果操作失败，重新加载数据恢复状态
              this.loadAlbumImages(true);
            }
          } catch (error) {
            hideToast(this);
            console.error('批量删除失败:', error);
            showError(this, '删除失败，请重试');
            // 发生错误时重新加载数据确保一致性
            this.loadAlbumImages(true);
          }
        }
      }
    });
  },

  // 从当前文件夹移除单张图片
  removeImageFromCurrentFolder: async function(imageId) {
    const { currentFolder } = this.data;

    if (!currentFolder) {
      showError(this, '当前不在文件夹中');
      return;
    }

    // 只有自定义文件夹支持移除操作
    if (currentFolder.type !== 'custom') {
      showError(this, '只有自定义文件夹支持移除操作');
      return;
    }

    wx.showModal({
      title: '移除图片',
      content: `确定要从"${currentFolder.name}"文件夹中移除这张图片吗？图片本身不会被删除。`,
      confirmText: '移除',
      confirmColor: '#ff9500',
      success: async (res) => {
        if (res.confirm) {
          showLoading(this, '移除中...');

          try {
            const result = await removeImageFromFolder(imageId, currentFolder._id);
            hideToast(this);

            if (result.success) {
              showToast(this, { message: '已从文件夹移除', theme: 'success' });
              // 智能更新：从当前文件夹视图中移除图片
              this.removeImagesFromList([imageId]);
              // 更新当前文件夹数量
              if (this.data.currentFolder) {
                this.updateSingleFolderCount(this.data.currentFolder._id, this.data.currentFolder.type);
              }
            } else {
              showError(this, result.message || '移除失败');
            }
          } catch (error) {
            hideToast(this);
            console.error('移除图片失败:', error);
            showError(this, '移除失败');
          }
        }
      }
    });
  },

  // 批量从当前文件夹移除图片
  batchRemoveFromCurrentFolder: async function() {
    const { selectedImages, currentFolder } = this.data;

    if (!currentFolder) {
      showError(this, '当前不在文件夹中');
      return;
    }

    // 只有自定义文件夹支持移除操作
    if (currentFolder.type !== 'custom') {
      showError(this, '只有自定义文件夹支持移除操作');
      return;
    }

    wx.showModal({
      title: '批量移除',
      content: `确定要从"${currentFolder.name}"文件夹中移除选中的${selectedImages.length}张图片吗？图片本身不会被删除。`,
      confirmText: '移除',
      confirmColor: '#ff9500',
      success: async (res) => {
        if (res.confirm) {
          showLoading(this, '移除中...');

          try {
            // 批量移除图片
            const promises = selectedImages.map(imageId =>
              removeImageFromFolder(imageId, currentFolder._id)
            );
            const results = await Promise.all(promises);

            hideToast(this);

            // 检查是否所有操作都成功
            const failedCount = results.filter(result => !result.success).length;

            if (failedCount === 0) {
              showToast(this, {
                message: `已从文件夹移除${selectedImages.length}张图片`,
                theme: 'success'
              });
            } else {
              showWarning(this, `${selectedImages.length - failedCount}张移除成功，${failedCount}张移除失败`);
            }

            this.onCancelSelection(); // 退出选择模式
            // 智能更新：从当前文件夹视图中移除图片
            this.removeImagesFromList(selectedImages);
            // 更新当前文件夹数量
            if (this.data.currentFolder) {
              this.updateSingleFolderCount(this.data.currentFolder._id, this.data.currentFolder.type);
            }
          } catch (error) {
            hideToast(this);
            console.error('批量移除图片失败:', error);
            showError(this, '移除失败');
          }
        }
      }
    });
  },

  // 获取当前用户的相册图片列表（保持兼容性）
  getAlbumImages: function() {
    // 重定向到新的加载方法
    this.loadAlbumImages(true);
  },

  // 激活首页图片选择模式
  onChooseBannerMode: function() {
    this.setData({ chooseBannerMode: true });
  },

  // 退出首页图片选择模式
  onCancelChooseBanner: function() {
    this.setData({ chooseBannerMode: false });
  },

  // 激活删除模式
  onDeleteMode: function() {
    // 进入批量删除模式时，为每张图片加selected字段，初始为false
    let albumImages = this.data.albumImages.map(img => ({ ...img, selected: false }));
    this.setData({ deleteMode: true, albumImages });
  },

  // 选择/取消选中图片（批量删除模式下）
  onSelectImage: function(e) {
    // 获取当前图片索引
    const index = e.currentTarget.dataset.index;
    let albumImages = this.data.albumImages;
    // 切换选中状态
    albumImages[index].selected = !albumImages[index].selected;
    this.setData({ albumImages });
  },

  // 退出批量删除模式
  onCancelDeleteMode: function() {
    // 直接退出批量删除模式，不执行删除操作
    // 删除操作应该通过专门的删除按钮触发，使用安全的软删除逻辑
    this.setData({ deleteMode: false });
  },

  // 选择/取消首页图片（仅在选择模式下可用）
  onToggleBanner: function(e) {
    if (!this.data.chooseBannerMode) {
      // 不是选择模式，转为预览图片
      this.onPreviewImage(e);
      return;
    }
    const index = e.currentTarget.dataset.index;
    let { albumImages, maxBannerCount } = this.data;
    let img = albumImages[index];
    const selected = albumImages.filter(i => i.bannerOrder).sort((a, b) => a.bannerOrder - b.bannerOrder);

    if (img.bannerOrder) {
      const oldOrder = img.bannerOrder;
      img.bannerOrder = null;
      albumImages.forEach(i => {
        if (i.bannerOrder && i.bannerOrder > oldOrder) {
          i.bannerOrder--;
        }
      });
    } else {
      if (selected.length >= maxBannerCount) {
        showToast(this, { message: '最多只能选5张首页图片', theme: 'warning' });
        return;
      }
      img.bannerOrder = selected.length + 1;
    }

    showLoading(this, '保存中...');
    db.collection('album_images').doc(img._id).update({
      data: {
        bannerOrder: img.bannerOrder,
        updateTime: new Date()
      },
      success: () => {
        hideToast(this);
        // 智能更新：立即更新UI中的图片状态
        this.updateImageInList(img._id, { bannerOrder: img.bannerOrder });
        // 只更新首页展示文件夹数量
        this.updateSingleFolderCount('folder_banner', 'system', 'banner');
      },
      fail: () => {
        hideToast(this);
        showError(this, '保存失败');
      }
    });
  },



  // 上传图片
  onUploadImage: function() {
    // 防止重复上传
    if (this.data.isUploading) return;

    // 检查用户登录状态
    const app = getApp();
    if (!app.isLoggedIn()) {
      showError(this, '请先登录后再上传图片');
      return;
    }

    const userInfo = app.getUserInfo();
    if (!userInfo || !userInfo.openid) {
      showError(this, '用户信息无效，请重新登录');
      return;
    }

    wx.chooseImage({
      count: 9, // 一次最多可选9张图片（小程序限制）
      sizeType: ['compressed'], // 使用压缩图片
      sourceType: ['album', 'camera'],
      success: chooseRes => {
        const filePaths = chooseRes.tempFilePaths; // 本地临时路径数组
        if (filePaths.length === 0) return;

        // 设置上传状态
        this.setData({ isUploading: true });

        // 用Promise.all批量上传所有图片
        const uploadTasks = filePaths.map(filePath => {
          // 生成云存储路径，index-images/ 文件夹下，文件名用时间戳+随机数
          const timestamp = Date.now();
          const randomStr = Math.random().toString(36).substring(2, 11);
          const cloudPath = `index-images/${timestamp}_${randomStr}.jpg`;

          return wx.cloud.uploadFile({
            cloudPath,
            filePath
          }).then(uploadRes => {
            // 上传成功后，将 fileID 存入数据库（包含用户信息和时间）
            const uploadTime = new Date();
            return db.collection('album_images').add({
              data: {
                fileID: uploadRes.fileID,
                createTime: uploadTime,
                updateTime: uploadTime,
                // 新增：记录上传用户信息
                uploadedBy: {
                  openid: userInfo.openid,
                  nickName: userInfo.nickName || '未知用户',
                  avatarUrl: userInfo.avatarUrl || '',
                  role: userInfo.role || '学员'
                },
                uploadTime: uploadTime, // 明确的上传时间字段
                isFavorite: false,
                folderIds: [],
                isDeleted: false,
                bannerOrder: null
              }
            });
          });
        });

        // 全部上传完成后刷新图片列表
        Promise.all(uploadTasks).then(() => {
          showToast(this, { message: '上传成功', theme: 'success' });
          // 智能更新：重新加载图片列表（上传是新增操作，需要重新加载）
          this.loadAlbumImages(true);
        }).catch(error => {
          console.error('图片上传失败:', error);
          showError(this, '有图片上传失败');
        }).finally(() => {
          // 重置上传状态
          this.setData({ isUploading: false });
        });
      },
      fail: () => {
        // 用户取消选择图片
        this.setData({ isUploading: false });
      }
    });
  },

  // 删除图片（安全的软删除）
  onDeleteImage: function(e) {
    const id = e.currentTarget.dataset.id; // 数据库记录id
    // 弹窗提示用户确认
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      confirmText: '删除',
      confirmColor: '#E34D59', // 红色
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户点击了“删除”
          // 使用安全的软删除方法
          this.deleteImage(id);
        }
        // 用户点击“取消”则不做任何操作
      }
    });
  },

  // 预览图片（仅在非选择/删除模式下可用）
  onPreviewImage: function(e) {
    if (this.data.chooseBannerMode || this.data.deleteMode) return; // 选择或删除模式下不响应
    const index = e.currentTarget.dataset.index;
    const urls = this.data.albumImages.map(item => item.tempFileURL);
    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  },



  // 确保文件夹存在 - 保留原方法作为备用
  ensureFoldersExist: async function() {
    try {
      // 先尝试加载文件夹
      await this.loadFolders();

      // 检查是否缺少必要的系统文件夹
      await this.ensureSystemFoldersExist();

      // 如果没有文件夹，创建所有系统文件夹
      if (this.data.folders.length === 0) {
        await this.createSystemFoldersIfNeeded();
        await this.loadFolders();
      }
    } catch (error) {
      console.error('确保文件夹存在时出错:', error);
    }
  },

  // 确保系统文件夹存在（特别是回收站）
  ensureSystemFoldersExist: async function() {
    try {
      const requiredSystemFolders = [
        {
          id: 'folder_favorite',
          name: '收藏夹',
          systemType: 'favorite'
        },
        {
          id: 'folder_banner',
          name: '首页展示',
          systemType: 'banner'
        },
        {
          id: 'folder_trash',
          name: '回收站',
          systemType: 'trash'
        }
      ];

      // 检查每个必需的系统文件夹是否存在
      for (const requiredFolder of requiredSystemFolders) {
        const exists = this.data.folders.some(folder =>
          folder._id === requiredFolder.id && folder.type === 'system'
        );

        if (!exists) {
          console.log(`系统文件夹 ${requiredFolder.name} 不存在，正在创建...`);
          await this.createSingleSystemFolder(requiredFolder);
        }
      }

      // 重新加载文件夹列表
      await this.loadFolders();
    } catch (error) {
      console.error('确保系统文件夹存在时出错:', error);
    }
  },

  // 创建单个系统文件夹
  createSingleSystemFolder: async function(folderConfig) {
    try {
      const folderData = {
        name: folderConfig.name,
        type: 'system',
        systemType: folderConfig.systemType,
        createTime: new Date(),
        updateTime: new Date(),
        imageCount: 0
      };

      await db.collection('album_folders').doc(folderConfig.id).set({
        data: folderData
      });

      console.log(`系统文件夹 ${folderConfig.name} 创建成功`);
    } catch (error) {
      if (error.errCode === -502002) {
        // 文档已存在，这是正常情况
        console.log(`系统文件夹 ${folderConfig.name} 已存在`);
      } else {
        console.error(`创建系统文件夹 ${folderConfig.name} 失败:`, error);
        throw error;
      }
    }
  },

  // 更新所有文件夹的图片数量 - 优化版：批量查询，解决N+1问题
  updateAllFolderCounts: async function(folders) {
    try {
      console.log('🚀 开始批量更新文件夹数量，避免N+1查询问题');

      // 🔧 优化：使用批量查询替代N+1查询
      const [
        allImages,           // 所有图片
        favoriteImages,      // 收藏图片
        bannerImages,        // 首页展示图片
        deletedImages        // 回收站图片
      ] = await Promise.all([
        // 查询所有未删除的图片
        db.collection('album_images').where({
          isDeleted: db.command.neq(true)
        }).get(),

        // 查询收藏图片
        db.collection('album_images').where({
          isFavorite: true,
          isDeleted: db.command.neq(true)
        }).get(),

        // 查询首页展示图片
        db.collection('album_images').where({
          bannerOrder: db.command.neq(null),
          isDeleted: db.command.neq(true)
        }).get(),

        // 查询回收站图片
        db.collection('album_images').where({
          isDeleted: true
        }).get()
      ]);

      // 🔧 批量计算文件夹数量
      folders.forEach(folder => {
        if (folder.systemType === 'favorite') {
          folder.imageCount = favoriteImages.data.length;
        } else if (folder.systemType === 'banner') {
          folder.imageCount = bannerImages.data.length;
        } else if (folder.systemType === 'trash') {
          folder.imageCount = deletedImages.data.length;
        } else if (folder.type === 'custom') {
          // 自定义文件夹：计算包含该文件夹ID的图片数量
          folder.imageCount = allImages.data.filter(img =>
            (img.folderIds || []).includes(folder._id)
          ).length;
        } else {
          folder.imageCount = 0;
        }
      });

      console.log('✅ 批量更新文件夹数量完成，避免了N+1查询');
    } catch (error) {
      console.error('批量更新文件夹数量失败:', error);
      // 降级到单个查询
      console.log('🔄 降级到单个查询模式');
      for (const folder of folders) {
        try {
          const count = await this.updateSingleFolderCount(folder._id, folder.type, folder.systemType);
          folder.imageCount = count;
        } catch (singleError) {
          console.warn(`文件夹 ${folder.name} 数量更新失败:`, singleError);
          folder.imageCount = 0;
        }
      }
    }
  },

  // 更新单个文件夹的图片数量
  updateSingleFolderCount: async function(folderId, folderType, systemType) {
    try {
      let count = 0;

      if (folderType === 'system') {
        if (systemType === 'favorite') {
          // 收藏夹：统计收藏的图片
          const result = await db.collection('album_images')
            .where({
              isFavorite: true,
              isDeleted: db.command.neq(true) // 兼容旧数据
            })
            .count();
          count = result.total;
        } else if (systemType === 'banner') {
          // 首页展示：统计有bannerOrder的图片
          const result = await db.collection('album_images')
            .where({
              bannerOrder: db.command.neq(null),
              isDeleted: db.command.neq(true) // 兼容旧数据
            })
            .count();
          count = result.total;
        } else if (systemType === 'trash') {
          // 回收站：统计已删除的图片
          const result = await db.collection('album_images')
            .where({
              isDeleted: true
            })
            .count();
          count = result.total;
        }
      } else {
        // 自定义文件夹：统计包含该文件夹ID的图片
        const result = await db.collection('album_images')
          .where({
            folderIds: db.command.in([folderId]),
            isDeleted: db.command.neq(true) // 兼容旧数据
          })
          .count();
        count = result.total;
      }

      // 更新数据库中的文件夹图片数量
      await db.collection('album_folders').doc(folderId).update({
        data: {
          imageCount: count,
          updateTime: new Date()
        }
      });

      return count;
    } catch (error) {
      console.error(`更新文件夹 ${folderId} 图片数量失败:`, error);
      return 0;
    }
  },

  // 确保回收站文件夹存在（在删除操作前调用）
  ensureTrashFolderExists: async function() {
    try {
      // 检查回收站文件夹是否存在
      const trashFolder = await db.collection('album_folders').doc('folder_trash').get();

      if (!trashFolder.data) {
        // 回收站文件夹不存在，创建它
        console.log('回收站文件夹不存在，正在创建...');
        await this.createSingleSystemFolder({
          id: 'folder_trash',
          name: '回收站',
          systemType: 'trash'
        });
        console.log('回收站文件夹创建成功');
      }
    } catch (error) {
      if (error.errCode === -502001) {
        // 文档不存在，创建回收站文件夹
        console.log('回收站文件夹不存在，正在创建...');
        await this.createSingleSystemFolder({
          id: 'folder_trash',
          name: '回收站',
          systemType: 'trash'
        });
        console.log('回收站文件夹创建成功');
      } else {
        console.error('检查回收站文件夹时出错:', error);
      }
    }
  },

  // 向上移动图片
  onMoveUp: function(e) {
    const index = e.currentTarget.dataset.index;
    if (index <= 0) return;

    const albumImages = [...this.data.albumImages];
    // 交换位置
    [albumImages[index], albumImages[index - 1]] = [albumImages[index - 1], albumImages[index]];

    this.setData({ albumImages });

    // 提供触觉反馈
    wx.vibrateShort();
  },

  // 向下移动图片
  onMoveDown: function(e) {
    const index = e.currentTarget.dataset.index;
    const { albumImages } = this.data;

    if (index >= albumImages.length - 1) return;

    const newAlbumImages = [...albumImages];
    // 交换位置
    [newAlbumImages[index], newAlbumImages[index + 1]] = [newAlbumImages[index + 1], newAlbumImages[index]];

    this.setData({ albumImages: newAlbumImages });

    // 提供触觉反馈
    wx.vibrateShort();
  },

  // 确认排序
  onConfirmSort: async function() {
    try {
      showLoading(this, '保存排序中...');

      const { albumImages } = this.data;

      // 重新分配bannerOrder
      const updatePromises = albumImages.map((image, index) => {
        const newOrder = index + 1;
        return db.collection('album_images').doc(image._id).update({
          data: { bannerOrder: newOrder }
        });
      });

      await Promise.all(updatePromises);

      hideToast(this);
      showToast(this, { message: '排序已保存', theme: 'success' });

      // 退出排序模式
      this.setData({
        isSortMode: false,
        originalOrder: [],
        showSortButtons: false,
        dragStartIndex: -1,
        dragOverIndex: -1,
        isDragging: false
      });

      // 智能更新：排序已经在UI中完成，只需要更新首页展示文件夹数量
      this.updateSingleFolderCount('folder_banner', 'system', 'banner');

    } catch (error) {
      hideToast(this);
      console.error('保存排序失败:', error);
      showError(this, '保存排序失败');
    }
  },

  // 取消排序
  onCancelSort: function() {
    const { originalOrder } = this.data;

    // 恢复原始排序
    const albumImages = [...this.data.albumImages];
    albumImages.sort((a, b) => {
      const orderA = originalOrder.find(item => item._id === a._id)?.bannerOrder || 0;
      const orderB = originalOrder.find(item => item._id === b._id)?.bannerOrder || 0;
      return orderA - orderB;
    });

    this.setData({
      isSortMode: false,
      originalOrder: [],
      albumImages: albumImages,
      showSortButtons: false,
      dragStartIndex: -1,
      dragOverIndex: -1,
      isDragging: false,
      dragOffsetX: 0,
      dragOffsetY: 0,
      lastMoveTime: 0,
      dragOffsetX: 0,
      dragOffsetY: 0,
      lastMoveTime: 0
    });

    showToast(this, { message: '已取消排序', theme: 'info' });
  },



  // 移动数组元素（用于拖拽排序）
  moveArrayElement: function(array, fromIndex, toIndex) {
    const element = array[fromIndex];
    array.splice(fromIndex, 1);
    array.splice(toIndex, 0, element);
    return array;
  },

  // 更新图片的文件夹关联
  updateImageFolders: async function(imageId, newFolderIds, oldFolderIds) {
    try {
      const updateData = {};

      // 处理回收站特殊逻辑
      if (newFolderIds.includes('folder_trash')) {
        // 如果要移入回收站，使用专门的回收站逻辑
        return await moveToTrash(imageId);
      }

      // 处理系统文件夹
      const isFavorite = newFolderIds.includes('folder_favorite');
      const isInBanner = newFolderIds.includes('folder_banner');

      updateData.isFavorite = isFavorite;
      updateData.isDeleted = false; // 确保不在回收站

      // 处理首页展示
      if (isInBanner) {
        // 如果要加入首页展示，需要分配bannerOrder
        if (!oldFolderIds.includes('folder_banner')) {
          // 获取当前最大的bannerOrder
          const bannerImages = this.data.albumImages.filter(img => img.bannerOrder);
          const maxOrder = bannerImages.length > 0 ? Math.max(...bannerImages.map(img => img.bannerOrder)) : 0;
          updateData.bannerOrder = maxOrder + 1;
        }
        // 如果已经在首页展示中，保持原有的bannerOrder
      } else {
        // 如果要从首页展示中移除
        updateData.bannerOrder = null;
      }

      // 处理自定义文件夹
      const customFolderIds = newFolderIds.filter(id =>
        id !== 'folder_favorite' &&
        id !== 'folder_banner' &&
        id !== 'folder_trash'
      );
      updateData.folderIds = customFolderIds;
      updateData.updateTime = new Date();

      // 清理可能存在的回收站相关字段
      if (oldFolderIds.includes('folder_trash')) {
        updateData.deletedTime = db.command.remove();
        updateData.beforeDeleteInfo = db.command.remove();
      }

      // 更新数据库
      await db.collection('album_images').doc(imageId).update({
        data: updateData
      });

      return { success: true };
    } catch (error) {
      console.error('更新图片文件夹失败:', error);
      return { success: false, error };
    }
  },

  // 删除自定义文件夹
  deleteCustomFolder: async function(folderId, folderName) {
    try {
      showLoading(this, '删除中...');

      // 1. 从所有图片中移除该文件夹ID
      const imagesResult = await db.collection('album_images')
        .where({
          folderIds: db.command.in([folderId])
        })
        .get();

      if (imagesResult.data.length > 0) {
        const updatePromises = imagesResult.data.map(image => {
          const newFolderIds = (image.folderIds || []).filter(id => id !== folderId);
          return db.collection('album_images').doc(image._id).update({
            data: {
              folderIds: newFolderIds,
              updateTime: new Date()
            }
          });
        });

        await Promise.all(updatePromises);
      }

      // 2. 删除文件夹文档
      await db.collection('album_folders').doc(folderId).remove();

      hideToast(this);
      showToast(this, {
        message: `文件夹"${folderName}"已删除`,
        theme: 'success'
      });

      // 3. 重新加载文件夹列表
      await this.loadFolders();

      // 4. 如果当前正在查看被删除的文件夹，返回文件夹列表
      if (this.data.currentFolder && this.data.currentFolder._id === folderId) {
        this.onBackToFolders();
      }

    } catch (error) {
      hideToast(this);
      console.error('删除文件夹失败:', error);
      showError(this, '删除文件夹失败');
    }
  },

  // ================================
  // 图片详情功能
  // ================================

  /**
   * 显示图片详情
   * @param {string} imageId - 图片ID
   */
  showImageInfo: async function(imageId) {
    try {
      showLoading(this, '加载详情中...');

      // 从数据库获取完整的图片信息
      const result = await db.collection('album_images').doc(imageId).get();

      if (!result.data) {
        hideToast(this);
        showError(this, '图片信息不存在');
        return;
      }

      const imageData = result.data;

      // 格式化时间
      const formatTime = (date) => {
        if (!date) return '未知';
        const d = new Date(date);
        return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`;
      };

      // 获取文件夹名称
      const folderNames = await this.getFolderNames(imageData);

      // 构建详情信息
      const imageInfo = {
        ...imageData,
        uploadTimeFormatted: formatTime(imageData.uploadTime || imageData.createTime),
        updateTimeFormatted: formatTime(imageData.updateTime),
        folderNames: folderNames,
        uploadedBy: imageData.uploadedBy || {
          nickName: '未知用户',
          role: '未知',
          openid: '未知'
        }
      };

      hideToast(this);

      // 显示详情对话框
      this.setData({
        showImageInfo: true,
        imageInfo: imageInfo
      });

    } catch (error) {
      hideToast(this);
      console.error('获取图片详情失败:', error);
      showError(this, '获取图片详情失败');
    }
  },

  /**
   * 获取图片所在文件夹的名称
   * @param {Object} imageData - 图片数据
   * @returns {string} 文件夹名称字符串
   */
  getFolderNames: async function(imageData) {
    const folderNames = [];

    // 检查系统文件夹
    if (imageData.isFavorite) {
      folderNames.push('收藏夹');
    }

    if (imageData.bannerOrder !== null && imageData.bannerOrder !== undefined) {
      folderNames.push('首页展示');
    }

    if (imageData.isDeleted) {
      folderNames.push('回收站');
    }

    // 检查自定义文件夹
    if (imageData.folderIds && imageData.folderIds.length > 0) {
      try {
        const folderResult = await db.collection('album_folders')
          .where({
            _id: db.command.in(imageData.folderIds)
          })
          .get();

        folderResult.data.forEach(folder => {
          folderNames.push(folder.name);
        });
      } catch (error) {
        console.warn('获取自定义文件夹名称失败:', error);
      }
    }

    return folderNames.length > 0 ? folderNames.join('、') : '无';
  },

  /**
   * 关闭图片详情对话框
   */
  onImageInfoCancel: function() {
    this.setData({
      showImageInfo: false,
      imageInfo: null
    });
  }

});