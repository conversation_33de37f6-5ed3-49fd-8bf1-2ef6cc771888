<!--
此文件为开发者工具生成，生成时间: 2025/8/2上午11:56:59
使用方法：
在 C:\Users\<USER>\WeChatProjects\miniprogram-6\miniprogram\pages\album-management\album-management.wxml 引入模板

```
<import src="album-management.skeleton.wxml"/>
<template is="skeleton" wx:if="{{loading}}" />
```

在 C:\Users\<USER>\WeChatProjects\miniprogram-6\miniprogram\pages\album-management\album-management.wxss 中引入样式
```
@import "./album-management.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
-->
<template name="skeleton">
  <view class="sk-container">
    <view class="album-container sk-image">
      <view class="main-content">
        <view class="tab-content">
          <view class="timeline-container">
            <view class="timeline-group">
              <view class="date-label">
                <text class="date-text sk-transparent sk-text-18-7500-573 sk-text">2025年7月31日</text>
                <text class="date-count sk-transparent sk-text-18-7500-353 sk-text">12张</text>
              </view>
              <view class="images-grid">
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons"></view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons"></view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons">
                    <view class="favorite-icon">
                      <view class="t-icon icon--t-icon  " style="color: #ff4757; font-size: 16px;">
                        <label class="t-icon-heart-filled icon--t-icon-heart-filled t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
                      </view>
                    </view>
                  </view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons">
                    <view class="favorite-icon">
                      <view class="t-icon icon--t-icon  " style="color: #ff4757; font-size: 16px;">
                        <label class="t-icon-heart-filled icon--t-icon-heart-filled t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
                      </view>
                    </view>
                  </view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons"></view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons"></view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons">
                    <view class="favorite-icon">
                      <view class="t-icon icon--t-icon  " style="color: #ff4757; font-size: 16px;">
                        <label class="t-icon-heart-filled icon--t-icon-heart-filled t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
                      </view>
                    </view>
                  </view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons"></view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons"></view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons"></view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons"></view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons"></view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
              </view>
            </view>
            <view class="timeline-group">
              <view class="date-label">
                <text class="date-text sk-transparent sk-text-18-7500-939 sk-text">2025年7月25日</text>
                <text class="date-count sk-transparent sk-text-18-7500-339 sk-text">1张</text>
              </view>
              <view class="images-grid">
                <view class="image-item" data-image="[object Object]">
                  <view class="status-icons"></view>
                  <image class="grid-image sk-image" lazy-load="true" mode="aspectFill"></image>
                </view>
              </view>
            </view>
            <view class="timeline-group">
              <view class="date-label">
                <text class="date-text sk-transparent sk-text-18-7500-602 sk-text">2025年7月24日</text>
                <text class="date-count sk-transparent sk-text-18-7500-729 sk-text">5张</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="bottom-tabs">
        <view class="tab-item active" data-tab="all">
          <view class="t-icon icon--t-icon  " style="color: #0052d9; font-size: 20px;">
            <label class="t-icon-image icon--t-icon-image t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
          </view>
          <text class="tab-text sk-transparent sk-text-18-7500-873 sk-text">全部图片</text>
        </view>
        <view class="tab-item" data-tab="folders">
          <view class="t-icon icon--t-icon  " style="color: #999; font-size: 20px;">
            <label class="t-icon-folder icon--t-icon-folder t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
          </view>
          <text class="tab-text sk-transparent sk-text-18-7500-31 sk-text">文件夹</text>
        </view>
        <view class="tab-item menu-tab">
          <view class="t-icon icon--t-icon  " style="color: #999; font-size: 20px;">
            <label class="t-icon-view-list icon--t-icon-view-list t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
          </view>
          <text class="tab-text sk-transparent sk-text-18-7500-9 sk-text">菜单</text>
        </view>
      </view>
    </view>
  </view>
</template>