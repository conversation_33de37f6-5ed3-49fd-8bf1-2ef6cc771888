<!--
此文件为开发者工具生成，生成时间: 2025/8/1下午10:45:18
使用方法：
在 C:\Users\<USER>\WeChatProjects\miniprogram-6\miniprogram\pages\index\index.wxml 引入模板

```
<import src="index.skeleton.wxml"/>
<template is="skeleton" wx:if="{{loading}}" />
```

在 C:\Users\<USER>\WeChatProjects\miniprogram-6\miniprogram\pages\index\index.wxss 中引入样式
```
@import "./index.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
-->
<template name="skeleton">
  <view class="sk-container">
    <view class="container">
      <view class="parallax-header">
        <image class="parallax-bg sk-image" mode="contain"></image>
        <view class="parallax-overlay sk-image"></view>
        <view class="parallax-content">
          <view class="logo  t-image image--t-image">
            <image class="t-image__img image--t-image__img t-image--shape-round image--t-image--shape-round sk-image" id="ecff2ddc--image" mode="aspectFill" style="width: 60px;height: 60px;"></image>
          </view>
          <text class="title sk-transparent sk-text-18-7500-537 sk-text">伽House</text>
        </view>
      </view>
      <view class="contact-info-container" hover-class="interactive-press" hover-stay-time="100">
        <view class="contact-row">
          <view class="contact-item contact-item-phone">
            <view class="t-icon icon--t-icon contact-icon ">
              <label class="t-icon-call icon--t-icon-call t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
            </view>
            <view class="contact-content">
              <text class="contact-label text-unify sk-transparent sk-text-18-7500-719 sk-text">联系电话</text>
              <text class="contact-value text-unify sk-transparent sk-text-14-2857-972 sk-text"> </text>
            </view>
          </view>
          <view class="contact-item contact-item-address">
            <view class="t-icon icon--t-icon contact-icon ">
              <label class="t-icon-location icon--t-icon-location t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
            </view>
            <view class="contact-content">
              <text class="contact-label text-unify sk-transparent sk-text-18-7500-925 sk-text">地址</text>
              <text class="contact-value text-unify contact-value-limited sk-transparent sk-text-14-2857-152 sk-text"> </text>
              <view class="address-more">
                <view class="t-icon icon--t-icon  " style="font-size: 12px;">
                  <label class="t-icon-chevron-right icon--t-icon-chevron-right t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="announcement-container">
          <view class="t-icon icon--t-icon announcement-icon ">
            <label class="t-icon-notification icon--t-icon-notification t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
          </view>
          <view class="announcement-content">
            <text class="announcement-label text-unify sk-transparent sk-text-18-7500-104 sk-text">公告</text>
            <text class="announcement-text announcement-text-limited text-unify sk-transparent sk-text-16-6667-549 sk-text">  </text>
            <view class="announcement-more">
              <view class="t-icon icon--t-icon  " style="font-size: 12px;">
                <label class="t-icon-chevron-right icon--t-icon-chevron-right t-icon-base icon--t-icon-base sk-pseudo sk-pseudo-circle"></label>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="static-images-container">
        <view class="static-image-wrapper" hover-class="interactive-press" hover-stay-time="100">
          <view class="static-image  t-image image--t-image">
            <image class="t-image__img image--t-image__img t-image--shape-round image--t-image--shape-round sk-image" id="407a3a90--image" mode="widthFix" style="height: 222.249px;"></image>
          </view>
        </view>
        <view class="static-image-wrapper" hover-class="interactive-press" hover-stay-time="100">
          <view class="static-image  t-image image--t-image">
            <image class="t-image__img image--t-image__img t-image--shape-round image--t-image--shape-round sk-image" id="5808ea80--image" mode="widthFix" style="height: 228.885px;"></image>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>